import type { CollectionBeforeLoginHook, CollectionAfterLoginHook } from 'payload';
import { syncAuth0User, getUserByAuth0Id } from '../auth/auth0-strategy';

// Re-export for backward compatibility
export { syncAuth0User, getUserByAuth0Id };

// Hook to sync user data before login
export const beforeLoginHook: CollectionBeforeLoginHook = async ({ req }) => {
  // This hook can be used for pre-login validation if needed
  return true;
};

// Hook to sync user data after login
export const afterLoginHook: CollectionAfterLoginHook = async ({ req, user, token }) => {
  try {
    // Update last login timestamp
    if (user && req.payload) {
      await req.payload.update({
        collection: 'users',
        id: user.id,
        data: {
          lastLoginAt: new Date(),
        },
      });
    }
  } catch (error) {
    console.error('Error in afterLoginHook:', error);
  }

  return user;
};

// Custom authentication function for Auth0 integration
export const authenticateWithAuth0 = async (payload: any, auth0Token: string) => {
  try {
    // Verify Auth0 token and get user info
    const response = await fetch(`https://${process.env.AUTH0_DOMAIN}/userinfo`, {
      headers: {
        Authorization: `Bearer ${auth0Token}`,
      },
    });

    if (!response.ok) {
      throw new Error('Invalid Auth0 token');
    }

    const auth0User = await response.json();
    
    // Sync user with Payload CMS
    const user = await syncAuth0User(payload, auth0User);
    
    return user;
  } catch (error) {
    console.error('Error authenticating with Auth0:', error);
    throw error;
  }
};

// Middleware to handle Auth0 authentication in Payload admin
export const auth0AdminAuth = async (req: any, res: any, next: any) => {
  try {
    // Check for Auth0 token in headers
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      // Verify token with Auth0
      const response = await fetch(`https://${process.env.AUTH0_DOMAIN}/userinfo`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const auth0User = await response.json();
        
        // Get or create user in Payload CMS
        const user = await syncAuth0User(req.payload, auth0User);
        
        // Set user in request
        req.user = user;
        
        return next();
      }
    }
    
    // If no valid Auth0 token, continue with normal Payload auth
    next();
  } catch (error) {
    console.error('Auth0 admin auth error:', error);
    next();
  }
};

// Helper function to validate Auth0 JWT token
export const validateAuth0Token = async (token: string) => {
  try {
    const domain = process.env.AUTH0_DOMAIN || process.env.NEXT_PUBLIC_AUTH0_DOMAIN;

    if (!domain) {
      console.error('Auth0 domain not configured');
      return null;
    }

    const response = await fetch(`https://${domain}/userinfo`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error('Error validating Auth0 token:', error);
    return null;
  }
};

// Custom login function that works with Auth0
export const loginWithAuth0 = async (payload: any, auth0Token: string) => {
  try {
    // Validate Auth0 token
    const auth0User = await validateAuth0Token(auth0Token);
    
    if (!auth0User) {
      throw new Error('Invalid Auth0 token');
    }

    // Sync user with Payload CMS
    const user = await syncAuth0User(payload, auth0User);
    
    // Generate Payload CMS token for the user
    const payloadToken = await payload.login({
      collection: 'users',
      data: {
        email: user.email,
      },
      req: {
        payload,
      },
    });

    return {
      user,
      token: payloadToken.token,
      exp: payloadToken.exp,
    };
  } catch (error) {
    console.error('Error logging in with Auth0:', error);
    throw error;
  }
};
