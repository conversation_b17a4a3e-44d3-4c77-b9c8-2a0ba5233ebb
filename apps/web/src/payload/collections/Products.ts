import type { CollectionConfig } from 'payload';
import { isSameFactory } from '../access/users';

export const Products: CollectionConfig = {
  slug: 'products',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'category', 'basePrice', 'status', 'factory'],
    group: 'Product Management',
  },
  access: {
    read: ({ req: { user } }: any): any => {
      if (user?.role === 'super_admin') return true;
      
      // Factory users can only see their own products
      if (user?.factory) {
        return {
          factory: {
            equals: user.factory,
          },
        };
      }
      
      // Customers can see all active products from verified factories
      if (user?.role === 'customer') {
        return {
          and: [
            {
              status: {
                equals: 'active',
              },
            },
            {
              'factory.verificationStatus': {
                equals: 'verified',
              },
            },
          ],
        };
      }
      
      return false;
    },
    create: isSameFactory,
    update: isSameFactory,
    delete: isSameFactory,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 200,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
    },
    {
      name: 'description',
      type: 'richText',
      required: false,
    },
    {
      name: 'shortDescription',
      type: 'textarea',
      maxLength: 500,
    },
    {
      name: 'factory',
      type: 'relationship',
      relationTo: 'factories',
      required: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'category',
      type: 'relationship',
      relationTo: 'categories',
      required: true,
    },
    {
      type: 'tabs',
      tabs: [
        {
          label: 'Pricing & Inventory',
          fields: [
            {
              name: 'basePrice',
              type: 'number',
              required: true,
              min: 0,
            },
            {
              name: 'currency',
              type: 'select',
              required: true,
              defaultValue: 'USD',
              options: [
                { label: 'US Dollar (USD)', value: 'USD' },
                { label: 'Chinese Yuan (CNY)', value: 'CNY' },
                { label: 'Euro (EUR)', value: 'EUR' },
              ],
            },
            {
              name: 'minOrderQty',
              type: 'number',
              required: true,
              defaultValue: 1,
              min: 1,
            },
            {
              name: 'maxOrderQty',
              type: 'number',
              min: 1,
            },
            {
              name: 'stockQuantity',
              type: 'number',
              defaultValue: 0,
              min: 0,
            },
            {
              name: 'stockStatus',
              type: 'select',
              required: true,
              defaultValue: 'in_stock',
              options: [
                { label: 'In Stock', value: 'in_stock' },
                { label: 'Low Stock', value: 'low_stock' },
                { label: 'Out of Stock', value: 'out_of_stock' },
                { label: 'Backorder', value: 'backorder' },
              ],
            },
          ],
        },
        {
          label: 'Product Details',
          fields: [
            {
              name: 'sku',
              type: 'text',
              unique: true,
            },
            {
              name: 'model',
              type: 'text',
            },
            {
              name: 'brand',
              type: 'text',
            },
            {
              name: 'weight',
              type: 'number',
              min: 0,
              admin: {
                description: 'Weight in kilograms',
              },
            },
            {
              name: 'dimensions',
              type: 'group',
              fields: [
                {
                  name: 'length',
                  type: 'number',
                  min: 0,
                },
                {
                  name: 'width',
                  type: 'number',
                  min: 0,
                },
                {
                  name: 'height',
                  type: 'number',
                  min: 0,
                },
                {
                  name: 'unit',
                  type: 'select',
                  defaultValue: 'cm',
                  options: [
                    { label: 'Centimeters', value: 'cm' },
                    { label: 'Inches', value: 'in' },
                    { label: 'Meters', value: 'm' },
                  ],
                },
              ],
            },
            {
              name: 'materials',
              type: 'array',
              fields: [
                {
                  name: 'material',
                  type: 'text',
                  required: true,
                },
              ],
            },
            {
              name: 'colors',
              type: 'array',
              fields: [
                {
                  name: 'color',
                  type: 'text',
                  required: true,
                },
              ],
            },
            {
              name: 'tags',
              type: 'array',
              fields: [
                {
                  name: 'tag',
                  type: 'text',
                  required: true,
                },
              ],
            },
          ],
        },
        {
          label: 'Media',
          fields: [
            {
              name: 'images',
              type: 'array',
              required: true,
              minRows: 1,
              fields: [
                {
                  name: 'image',
                  type: 'upload',
                  relationTo: 'media',
                  required: true,
                },
                {
                  name: 'alt',
                  type: 'text',
                },
                {
                  name: 'caption',
                  type: 'text',
                },
                {
                  name: 'isMain',
                  type: 'checkbox',
                  defaultValue: false,
                },
              ],
            },
          ],
        },
        {
          label: 'Settings',
          fields: [
            {
              name: 'status',
              type: 'select',
              required: true,
              defaultValue: 'draft',
              options: [
                { label: 'Draft', value: 'draft' },
                { label: 'Active', value: 'active' },
                { label: 'Inactive', value: 'inactive' },
                { label: 'Discontinued', value: 'discontinued' },
              ],
            },
            {
              name: 'isFeatured',
              type: 'checkbox',
              defaultValue: false,
            },
            {
              name: 'seoTitle',
              type: 'text',
              maxLength: 60,
            },
            {
              name: 'seoDescription',
              type: 'textarea',
              maxLength: 160,
            },
          ],
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Auto-assign factory from user context
          if (req.user?.factory && !data.factory) {
            data.factory = req.user.factory;
          }
          
          // Generate slug from name if not provided
          if (!data.slug && data.name) {
            data.slug = data.name
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '-')
              .replace(/(^-|-$)/g, '');
          }
        }
        return data;
      },
    ],
  },
};
