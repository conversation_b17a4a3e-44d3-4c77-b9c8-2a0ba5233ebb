import type { CollectionConfig } from 'payload';
import { isAdmin, isFactoryOwnerOrAdmin } from '../access/factories';

export const Factories: CollectionConfig = {
  slug: 'factories',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'email', 'verificationStatus', 'status', 'createdAt'],
    group: 'Factory Management',
  },
  access: {
    read: ({ req: { user } }) => {
      if (user?.role === 'super_admin') return true;
      if (user?.factory) {
        return {
          id: {
            equals: user.factory,
          },
        };
      }
      return false;
    },
    create: isAdmin,
    update: isFactoryOwnerOrAdmin,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      maxLength: 100,
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      admin: {
        description: 'URL-friendly identifier for the factory',
      },
    },
    {
      name: 'description',
      type: 'richText',
      required: false,
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'coverImage',
      type: 'upload',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'gallery',
      type: 'array',
      fields: [
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'caption',
          type: 'text',
        },
      ],
    },
    {
      type: 'tabs',
      tabs: [
        {
          label: 'Contact Information',
          fields: [
            {
              name: 'email',
              type: 'email',
              required: true,
            },
            {
              name: 'phone',
              type: 'text',
            },
            {
              name: 'website',
              type: 'text',
            },
            {
              name: 'address',
              type: 'group',
              fields: [
                {
                  name: 'street',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'city',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'state',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'postalCode',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'country',
                  type: 'text',
                  required: true,
                  defaultValue: 'China',
                },
              ],
            },
          ],
        },
        {
          label: 'Business Information',
          fields: [
            {
              name: 'businessType',
              type: 'select',
              required: true,
              options: [
                { label: 'Manufacturing', value: 'manufacturing' },
                { label: 'Trading Company', value: 'trading' },
                { label: 'OEM/ODM', value: 'oem_odm' },
                { label: 'Supplier', value: 'supplier' },
              ],
            },
            {
              name: 'industry',
              type: 'select',
              required: true,
              options: [
                { label: 'Electronics', value: 'electronics' },
                { label: 'Textiles', value: 'textiles' },
                { label: 'Machinery', value: 'machinery' },
                { label: 'Automotive', value: 'automotive' },
                { label: 'Home & Garden', value: 'home_garden' },
                { label: 'Other', value: 'other' },
              ],
            },
            {
              name: 'businessLicense',
              type: 'upload',
              relationTo: 'media',
              admin: {
                description: 'Upload business license document',
              },
            },
            {
              name: 'taxId',
              type: 'text',
            },
            {
              name: 'establishedYear',
              type: 'number',
              min: 1900,
              max: new Date().getFullYear(),
            },
            {
              name: 'employeeCount',
              type: 'select',
              options: [
                { label: '1-10', value: '1-10' },
                { label: '11-50', value: '11-50' },
                { label: '51-200', value: '51-200' },
                { label: '201-500', value: '201-500' },
                { label: '500+', value: '500+' },
              ],
            },
            {
              name: 'annualRevenue',
              type: 'select',
              options: [
                { label: 'Under $1M', value: 'under_1m' },
                { label: '$1M - $5M', value: '1m_5m' },
                { label: '$5M - $10M', value: '5m_10m' },
                { label: '$10M - $50M', value: '10m_50m' },
                { label: 'Over $50M', value: 'over_50m' },
              ],
            },
          ],
        },
        {
          label: 'Platform Settings',
          fields: [
            {
              name: 'currency',
              type: 'select',
              required: true,
              defaultValue: 'USD',
              options: [
                { label: 'US Dollar (USD)', value: 'USD' },
                { label: 'Chinese Yuan (CNY)', value: 'CNY' },
                { label: 'Euro (EUR)', value: 'EUR' },
                { label: 'British Pound (GBP)', value: 'GBP' },
              ],
            },
            {
              name: 'timezone',
              type: 'text',
              defaultValue: 'Asia/Shanghai',
            },
            {
              name: 'language',
              type: 'select',
              defaultValue: 'en',
              options: [
                { label: 'English', value: 'en' },
                { label: '简体中文', value: 'zh-CN' },
                { label: '繁體中文', value: 'zh-TW' },
              ],
            },
            {
              name: 'verificationStatus',
              type: 'select',
              required: true,
              defaultValue: 'pending',
              options: [
                { label: 'Pending', value: 'pending' },
                { label: 'Under Review', value: 'under_review' },
                { label: 'Verified', value: 'verified' },
                { label: 'Rejected', value: 'rejected' },
              ],
              admin: {
                description: 'Factory verification status',
              },
            },
            {
              name: 'status',
              type: 'select',
              required: true,
              defaultValue: 'active',
              options: [
                { label: 'Active', value: 'active' },
                { label: 'Inactive', value: 'inactive' },
                { label: 'Suspended', value: 'suspended' },
                { label: 'Pending Approval', value: 'pending_approval' },
              ],
            },
            {
              name: 'subscriptionTier',
              type: 'select',
              required: true,
              defaultValue: 'free',
              options: [
                { label: 'Free', value: 'free' },
                { label: 'Basic', value: 'basic' },
                { label: 'Standard', value: 'standard' },
                { label: 'Premium', value: 'premium' },
                { label: 'Enterprise', value: 'enterprise' },
              ],
            },
          ],
        },
      ],
    },
    // Onboarding and Capabilities Data
    {
      name: 'onboardingData',
      type: 'group',
      label: 'Onboarding & Capabilities',
      fields: [
        {
          name: 'onboardingCompleted',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Whether the factory has completed the onboarding wizard',
          },
        },
        {
          name: 'onboardingCompletedAt',
          type: 'date',
          admin: {
            description: 'When the onboarding was completed',
          },
        },
        {
          name: 'capabilities',
          type: 'group',
          label: 'Factory Capabilities',
          fields: [
            {
              name: 'productCategories',
              type: 'array',
              label: 'Product Categories',
              fields: [
                {
                  name: 'category',
                  type: 'text',
                },
              ],
            },
            {
              name: 'productionCapacity',
              type: 'text',
              label: 'Production Capacity',
            },
            {
              name: 'capacityUnit',
              type: 'text',
              label: 'Capacity Unit',
            },
            {
              name: 'shippingMethods',
              type: 'array',
              label: 'Shipping Methods',
              fields: [
                {
                  name: 'method',
                  type: 'text',
                },
              ],
            },
            {
              name: 'paymentMethods',
              type: 'array',
              label: 'Payment Methods',
              fields: [
                {
                  name: 'method',
                  type: 'text',
                },
              ],
            },
            {
              name: 'certifications',
              type: 'array',
              label: 'Certifications',
              fields: [
                {
                  name: 'certification',
                  type: 'text',
                },
              ],
            },
            {
              name: 'minimumOrderQuantity',
              type: 'text',
              label: 'Minimum Order Quantity',
            },
            {
              name: 'moqUnit',
              type: 'text',
              label: 'MOQ Unit',
            },
            {
              name: 'leadTime',
              type: 'text',
              label: 'Lead Time',
            },
          ],
        },
        {
          name: 'adminInfo',
          type: 'group',
          label: 'Admin Information',
          fields: [
            {
              name: 'adminName',
              type: 'text',
              label: 'Admin Name',
            },
            {
              name: 'adminEmail',
              type: 'email',
              label: 'Admin Email',
            },
            {
              name: 'adminPhone',
              type: 'text',
              label: 'Admin Phone',
            },
            {
              name: 'adminRole',
              type: 'text',
              label: 'Admin Role',
            },
            {
              name: 'department',
              type: 'text',
              label: 'Department',
            },
            {
              name: 'teamMembers',
              type: 'array',
              label: 'Team Members',
              fields: [
                {
                  name: 'name',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'email',
                  type: 'email',
                  required: true,
                },
                {
                  name: 'role',
                  type: 'text',
                  required: true,
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Generate slug from name if not provided
          if (!data.slug && data.name) {
            data.slug = data.name
              .toLowerCase()
              .replace(/[^a-z0-9]+/g, '-')
              .replace(/(^-|-$)/g, '');
          }
        }
        return data;
      },
    ],
  },
};
