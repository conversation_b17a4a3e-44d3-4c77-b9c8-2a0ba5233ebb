import type { CollectionConfig } from 'payload';
import { isAdmin, isAdminOrSelf } from '../access/users';

export const Users: CollectionConfig = {
  slug: 'users',
  auth: {
    useAPIKey: true,
    tokenExpiration: 7200, // 2 hours
    verify: {
      generateEmailHTML: ({ token, user }) => {
        return `
          <div>
            <h1>Welcome to FC-CHINA Factory Portal</h1>
            <p>Hello ${user.firstName},</p>
            <p>Please verify your email address by clicking the link below:</p>
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/verify?token=${token}">Verify Email</a>
          </div>
        `;
      },
    },
    forgotPassword: {
      generateEmailHTML: (args: any) => {
        const { token, user } = args || {};
        return `
          <div>
            <h1>Reset Your Password</h1>
            <p>Hello ${user.firstName},</p>
            <p>Click the link below to reset your password:</p>
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${token}">Reset Password</a>
          </div>
        `;
      },
    },
  },
  admin: {
    useAsTitle: 'email',
    defaultColumns: ['firstName', 'lastName', 'email', 'role', 'factory'],
    group: 'User Management',
  },
  access: {
    read: isAdminOrSelf,
    create: isAdmin,
    update: isAdminOrSelf,
    delete: isAdmin,
  },
  fields: [
    {
      name: 'firstName',
      type: 'text',
      required: true,
      maxLength: 50,
    },
    {
      name: 'lastName',
      type: 'text',
      required: true,
      maxLength: 50,
    },
    {
      name: 'email',
      type: 'email',
      required: true,
      unique: true,
    },
    {
      name: 'role',
      type: 'select',
      required: true,
      defaultValue: 'factory_staff',
      options: [
        {
          label: 'Super Admin',
          value: 'super_admin',
        },
        {
          label: 'Factory Owner',
          value: 'factory_owner',
        },
        {
          label: 'Factory Admin',
          value: 'factory_admin',
        },
        {
          label: 'Factory Manager',
          value: 'factory_manager',
        },
        {
          label: 'Factory Staff',
          value: 'factory_staff',
        },
        {
          label: 'Customer',
          value: 'customer',
        },
      ],
    },
    {
      name: 'factory',
      type: 'relationship',
      relationTo: 'factories',
      required: false,
      admin: {
        condition: (data, siblingData) => {
          return ['factory_owner', 'factory_admin', 'factory_manager', 'factory_staff'].includes(siblingData.role);
        },
      },
    },
    {
      name: 'avatar',
      type: 'upload',
      relationTo: 'media',
      required: false,
    },
    {
      name: 'phone',
      type: 'text',
      required: false,
    },
    {
      name: 'language',
      type: 'select',
      defaultValue: 'en',
      options: [
        { label: 'English', value: 'en' },
        { label: '简体中文', value: 'zh-CN' },
        { label: '繁體中文', value: 'zh-TW' },
      ],
    },
    {
      name: 'timezone',
      type: 'text',
      defaultValue: 'UTC',
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
    },
    {
      name: 'lastLoginAt',
      type: 'date',
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'auth0Id',
      type: 'text',
      unique: true,
      admin: {
        readOnly: true,
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          data.isActive = true;
        }
        return data;
      },
    ],
    afterLogin: [
      async ({ req, user }) => {
        // Update last login timestamp
        try {
          await req.payload.update({
            collection: 'users',
            id: user.id,
            data: {
              lastLoginAt: new Date(),
            },
          });
        } catch (error) {
          console.error('Error updating last login:', error);
        }
      },
    ],
    beforeLogin: [
      async ({ req }) => {
        // Pre-login validation hook
        return true;
      },
    ],
  },
};
