import type { CollectionConfig } from 'payload';
import { isSameFactory } from '../access/users';

export const Media: CollectionConfig = {
  slug: 'media',
  admin: {
    useAsTitle: 'filename',
    group: 'Media Management',
  },
  upload: {
    staticDir: 'media',
    imageSizes: [
      {
        name: 'thumbnail',
        width: 400,
        height: 300,
        position: 'centre',
      },
      {
        name: 'card',
        width: 768,
        height: 1024,
        position: 'centre',
      },
      {
        name: 'tablet',
        width: 1024,
        height: undefined,
        position: 'centre',
      },
    ],
    adminThumbnail: 'thumbnail',
    mimeTypes: ['image/*', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  },
  access: {
    read: ({ req: { user } }) => {
      if (user?.role === 'super_admin') return true;
      
      // Factory users can only see their own media
      if (user?.factory) {
        return {
          factory: {
            equals: user.factory,
          },
        };
      }
      
      return false;
    },
    create: ({ req: { user } }) => {
      return user?.factory ? true : false;
    },
    update: isSameFactory,
    delete: isSameFactory,
  },
  fields: [
    {
      name: 'alt',
      type: 'text',
      admin: {
        description: 'Alternative text for accessibility',
      },
    },
    {
      name: 'caption',
      type: 'text',
    },
    {
      name: 'factory',
      type: 'relationship',
      relationTo: 'factories',
      required: true,
      admin: {
        readOnly: true,
      },
    },
    {
      name: 'mediaType',
      type: 'select',
      required: true,
      defaultValue: 'image',
      options: [
        { label: 'Image', value: 'image' },
        { label: 'Document', value: 'document' },
        { label: 'Video', value: 'video' },
        { label: 'Other', value: 'other' },
      ],
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
          required: true,
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      ({ req, operation, data }) => {
        if (operation === 'create') {
          // Auto-assign factory from user context
          if (req.user?.factory && !data.factory) {
            data.factory = req.user.factory;
          }
          
          // Auto-detect media type from MIME type
          if (data.mimeType) {
            if (data.mimeType.startsWith('image/')) {
              data.mediaType = 'image';
            } else if (data.mimeType.startsWith('video/')) {
              data.mediaType = 'video';
            } else if (data.mimeType.includes('pdf') || data.mimeType.includes('document')) {
              data.mediaType = 'document';
            } else {
              data.mediaType = 'other';
            }
          }
        }
        return data;
      },
    ],
  },
};
