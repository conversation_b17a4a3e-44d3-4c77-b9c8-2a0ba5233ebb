import type { Access } from 'payload';

export const isAdmin: Access = ({ req: { user } }) => {
  return user?.role === 'super_admin';
};

export const isFactoryOwnerOrAdmin: Access = ({ req: { user } }) => {
  if (user?.role === 'super_admin') return true;
  
  if (['factory_owner', 'factory_admin'].includes(user?.role)) {
    // Users can only update their own factory
    return {
      id: {
        equals: user?.factory,
      },
    };
  }
  
  return false;
};

export const canReadFactory = ({ req: { user } }: any): any => {
  if (user?.role === 'super_admin') return true;

  // Factory users can only read their own factory
  if (user?.factory) {
    return {
      id: {
        equals: user.factory,
      },
    };
  }

  // Customers can read all verified factories
  if (user?.role === 'customer') {
    return {
      and: [
        {
          verificationStatus: {
            equals: 'verified',
          },
        },
        {
          status: {
            equals: 'active',
          },
        },
      ],
    };
  }

  return false;
};
