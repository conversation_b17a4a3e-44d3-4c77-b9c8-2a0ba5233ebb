import type { Access } from 'payload';

export const isAdmin: Access = ({ req: { user } }) => {
  return user?.role === 'super_admin';
};

export const isAdminOrSelf: Access = ({ req: { user } }) => {
  if (user?.role === 'super_admin') return true;
  
  return {
    id: {
      equals: user?.id,
    },
  };
};

export const isFactoryUser: Access = ({ req: { user } }) => {
  if (user?.role === 'super_admin') return true;
  
  if (['factory_owner', 'factory_admin', 'factory_manager', 'factory_staff'].includes(user?.role)) {
    return true;
  }
  
  return false;
};

export const isFactoryOwnerOrAdmin: Access = ({ req: { user } }) => {
  if (user?.role === 'super_admin') return true;
  
  if (['factory_owner', 'factory_admin'].includes(user?.role)) {
    return true;
  }
  
  return false;
};

export const isSameFactory: Access = ({ req: { user } }) => {
  if (user?.role === 'super_admin') return true;
  
  if (user?.factory) {
    return {
      factory: {
        equals: user.factory,
      },
    };
  }
  
  return false;
};
