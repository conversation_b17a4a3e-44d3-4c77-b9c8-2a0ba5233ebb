'use client';

import { useAuth } from '../contexts/auth-context';
import { LandingPage } from '../components/landing/landing-page';
import { DashboardLayout } from '../components/dashboard/dashboard-layout';
import { LoadingSpinner } from '../components/ui/loading-spinner';

export function HomeClient() {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-indigo-50">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (isAuthenticated) {
    return <DashboardLayout />;
  }

  return <LandingPage />;
}
