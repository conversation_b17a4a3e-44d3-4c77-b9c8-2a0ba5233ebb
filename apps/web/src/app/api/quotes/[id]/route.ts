import { NextRequest, NextResponse } from 'next/server';
import { Prisma } from '@prisma/client';
import { prisma, ensurePrismaConnection, withDatabaseRetry } from '../../../../lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    // Ensure database connection
    await ensurePrismaConnection();

    const { searchParams } = new URL(request.url);
    const factoryId = searchParams.get('factoryId');

    if (!factoryId) {
      return NextResponse.json(
        { error: 'Factory ID is required' },
        { status: 400 }
      );
    }

    console.log('🔍 Retrieving quote:', {
      quoteId: resolvedParams.id,
      factoryId
    });

    // Use retry wrapper for database operations
    const quote = await withDatabaseRetry(async () => {
      return await prisma.quote.findFirst({
        where: {
          id: resolvedParams.id,
          factoryId
        },
        include: {
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  basePrice: true,
                  currency: true,
                }
              },
              variant: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  price: true,
                }
              }
            }
          },
          factory: {
            select: {
              id: true,
              name: true,
              slug: true,
            }
          },
          order: {
            select: {
              id: true,
              orderNumber: true,
              status: true,
            }
          },
          messages: {
            include: {
              sender: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                }
              }
            },
            orderBy: { createdAt: 'desc' },
            take: 10
          }
        }
      });
    }, 'Quote retrieval');

    if (!quote) {
      return NextResponse.json(
        { error: 'Quote not found' },
        { status: 404 }
      );
    }

    console.log('✅ Quote retrieved successfully:', {
      id: quote.id,
      quoteNumber: quote.quoteNumber,
      status: quote.status,
      total: quote.total
    });

    return NextResponse.json(quote);

  } catch (error) {
    console.error('❌ Error retrieving quote:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve quote' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    // Ensure database connection
    await ensurePrismaConnection();

    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const factoryId = searchParams.get('factoryId');

    if (!factoryId) {
      return NextResponse.json(
        { error: 'Factory ID is required' },
        { status: 400 }
      );
    }

    console.log('🔄 Updating quote:', {
      quoteId: resolvedParams.id,
      factoryId,
      status: body.status
    });

    // Use retry wrapper for database operations
    const quote = await withDatabaseRetry(async () => {
      // First verify the quote exists and belongs to the factory
      const existingQuote = await prisma.quote.findFirst({
        where: {
          id: resolvedParams.id,
          factoryId
        },
        include: {
          items: true
        }
      });

      if (!existingQuote) {
        throw new Error('Quote not found');
      }

      // Calculate totals if items are provided
      let subtotal = existingQuote.subtotal;
      let total = existingQuote.total;

      if (body.items) {
        subtotal = body.items.reduce((sum: number, item: any) => {
          return sum + (item.quantity * item.unitPrice);
        }, 0);

        const tax = body.tax !== undefined ? parseFloat(body.tax) : (existingQuote.tax ? Number(existingQuote.tax) : 0);
        const shipping = body.shipping !== undefined ? parseFloat(body.shipping) : Number(existingQuote.shipping);
        total = new Prisma.Decimal(Number(subtotal) + Number(tax) + Number(shipping));
      }

      // Use transaction for complex updates
      return await prisma.$transaction(async (tx) => {
        // Handle items update if provided
        if (body.items) {
          // Delete existing items
          await tx.quoteItem.deleteMany({
            where: { quoteId: resolvedParams.id }
          });

          // Create new items
          if (body.items.length > 0) {
            await tx.quoteItem.createMany({
              data: body.items.map((item: any) => ({
                quoteId: resolvedParams.id,
                productId: item.productId,
                quantity: item.quantity,
                unitPrice: parseFloat(item.unitPrice),
                totalPrice: item.quantity * parseFloat(item.unitPrice),
                notes: item.notes || null,
              }))
            });
          }
        }

        // Update the quote
        return await tx.quote.update({
          where: { id: resolvedParams.id },
          data: {
            status: body.status || existingQuote.status,
            customerName: body.customerName || existingQuote.customerName,
            customerEmail: body.customerEmail || existingQuote.customerEmail,
            customerPhone: body.customerPhone !== undefined ? body.customerPhone : existingQuote.customerPhone,
            customerCompany: body.customerCompany !== undefined ? body.customerCompany : existingQuote.customerCompany,
            shippingAddress: body.shippingAddress !== undefined ? body.shippingAddress : existingQuote.shippingAddress,
            shippingMethod: body.shippingMethod !== undefined ? body.shippingMethod : existingQuote.shippingMethod,
            subtotal: subtotal,
            tax: body.tax !== undefined ? parseFloat(body.tax) : existingQuote.tax,
            shipping: body.shipping !== undefined ? parseFloat(body.shipping) : existingQuote.shipping,
            total: total,
            validUntil: body.validUntil ? new Date(body.validUntil) : existingQuote.validUntil,
            updatedAt: new Date(),
          },
          include: {
            items: {
              include: {
                product: {
                  select: {
                    id: true,
                    name: true,
                    sku: true,
                    basePrice: true,
                  }
                }
              }
            },
            factory: {
              select: {
                id: true,
                name: true,
                slug: true,
              }
            }
          }
        });
      });
    }, 'Quote update');

    console.log('✅ Quote updated successfully:', {
      id: quote.id,
      quoteNumber: quote.quoteNumber,
      status: quote.status
    });

    return NextResponse.json(quote);

  } catch (error: any) {
    console.error('❌ Error updating quote:', error);
    
    if (error.message === 'Quote not found') {
      return NextResponse.json(
        { error: 'Quote not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update quote' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    // Ensure database connection
    await ensurePrismaConnection();

    const { searchParams } = new URL(request.url);
    const factoryId = searchParams.get('factoryId');

    if (!factoryId) {
      return NextResponse.json(
        { error: 'Factory ID is required' },
        { status: 400 }
      );
    }

    console.log('🔄 Deleting quote:', {
      quoteId: resolvedParams.id,
      factoryId
    });

    // Use retry wrapper for database operations
    const result = await withDatabaseRetry(async () => {
      // First verify the quote exists and belongs to the factory
      const existingQuote = await prisma.quote.findFirst({
        where: {
          id: resolvedParams.id,
          factoryId
        },
        include: {
          order: true
        }
      });

      if (!existingQuote) {
        throw new Error('Quote not found');
      }

      // Check if quote has been converted to an order
      if (existingQuote.order) {
        throw new Error('Cannot delete quote that has been converted to an order');
      }

      // Delete the quote (items will be cascade deleted)
      await prisma.quote.delete({
        where: { id: resolvedParams.id }
      });

      return existingQuote;
    }, 'Quote deletion');

    console.log('✅ Quote deleted successfully:', {
      id: result.id,
      quoteNumber: result.quoteNumber,
      factoryId
    });

    return NextResponse.json({ 
      message: 'Quote deleted successfully',
      deletedQuote: {
        id: result.id,
        quoteNumber: result.quoteNumber
      }
    });

  } catch (error: any) {
    console.error('❌ Error deleting quote:', error);
    
    if (error.message === 'Quote not found') {
      return NextResponse.json(
        { error: 'Quote not found' },
        { status: 404 }
      );
    }

    if (error.message.includes('converted to an order')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete quote' },
      { status: 500 }
    );
  }
}
