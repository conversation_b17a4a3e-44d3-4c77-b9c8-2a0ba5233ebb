'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '../../../../contexts/auth-context';
import { ProtectedRoute } from '../../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../../components/dashboard/dashboard-sidebar';
import { Button } from '../../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Badge } from '../../../../components/ui/badge';
import { 
  ArrowLeft, 
  Edit,
  Trash2,
  FileText,
  Calendar,
  DollarSign,
  User,
  Building2,
  Package,
  AlertCircle,
  CheckCircle,
  Clock,
  ShoppingCart
} from 'lucide-react';
import Link from 'next/link';

// Force dynamic rendering to avoid prerendering issues with client components
export const dynamic = 'force-dynamic';

interface Quote {
  id: string;
  quoteNumber: string;
  status: 'PENDING' | 'SENT' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED' | 'CONVERTED';
  subtotal: number;
  tax: number;
  shipping: number;
  total: number;
  currency: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  customerCompany?: string;
  validUntil: string;
  createdAt: string;
  updatedAt: string;
  items: Array<{
    id: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    notes?: string;
    product: {
      id: string;
      name: string;
      sku?: string;
      basePrice: number;
    };
    variant?: {
      id: string;
      name: string;
      sku?: string;
    };
  }>;
  factory: {
    id: string;
    name: string;
    slug: string;
  };
  order?: {
    id: string;
    orderNumber: string;
    status: string;
  };
}

const statusColors = {
  PENDING: 'bg-yellow-100 text-yellow-800',
  SENT: 'bg-blue-100 text-blue-800',
  ACCEPTED: 'bg-green-100 text-green-800',
  REJECTED: 'bg-red-100 text-red-800',
  EXPIRED: 'bg-gray-100 text-gray-800',
  CONVERTED: 'bg-purple-100 text-purple-800',
};

const statusIcons = {
  PENDING: Clock,
  SENT: FileText,
  ACCEPTED: CheckCircle,
  REJECTED: AlertCircle,
  EXPIRED: Calendar,
  CONVERTED: ShoppingCart,
};

export default function QuoteDetailPage() {
  const { user } = useAuth();
  const params = useParams();
  const router = useRouter();
  const [quote, setQuote] = useState<Quote | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.factoryId && params.id) {
      loadQuote();
    }
  }, [user?.factoryId, params.id]);

  const loadQuote = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/quotes/${params.id}?factoryId=${user!.factoryId}`);
      
      if (response.ok) {
        const quoteData = await response.json();
        setQuote(quoteData);
        console.log('✅ Quote loaded:', quoteData.id);
      } else if (response.status === 404) {
        setError('Quote not found');
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to load quote');
      }
    } catch (error) {
      console.error('❌ Error loading quote:', error);
      setError('Failed to load quote. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteQuote = async () => {
    if (!quote || !confirm(`Are you sure you want to delete quote ${quote.quoteNumber}?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/quotes/${quote.id}?factoryId=${user!.factoryId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        console.log('✅ Quote deleted successfully');
        router.push('/dashboard/quotes');
      } else {
        const errorData = await response.json();
        alert(`Error deleting quote: ${errorData.error}`);
      }
    } catch (error) {
      console.error('❌ Error deleting quote:', error);
      alert('Failed to delete quote. Please try again.');
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const isExpired = (validUntil: string) => {
    return new Date(validUntil) < new Date();
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-gray-500">Loading quote...</div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error || !quote) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Quote Not Found</h3>
              <p className="text-gray-600 mb-6">{error || 'The requested quote could not be found.'}</p>
              <Button asChild>
                <Link href="/dashboard/quotes">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Quotes
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  const StatusIcon = statusIcons[quote.status];
  const expired = isExpired(quote.validUntil);

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/dashboard/quotes">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Quotes
                  </Link>
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">{quote.quoteNumber}</h1>
                  <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                    <div className="flex items-center">
                      <User className="w-4 h-4 mr-1" />
                      {quote.customerName}
                    </div>
                    {quote.customerCompany && (
                      <div className="flex items-center">
                        <Building2 className="w-4 h-4 mr-1" />
                        {quote.customerCompany}
                      </div>
                    )}
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      Valid until {formatDate(quote.validUntil)}
                      {expired && <span className="text-red-600 ml-1">(Expired)</span>}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Badge className={statusColors[quote.status]}>
                  <StatusIcon className="w-3 h-3 mr-1" />
                  {quote.status}
                </Badge>
                
                {quote.status !== 'CONVERTED' && (
                  <>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/dashboard/quotes/${quote.id}/edit`}>
                        <Edit className="w-4 h-4 mr-1" />
                        Edit
                      </Link>
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={handleDeleteQuote}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4 mr-1" />
                      Delete
                    </Button>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            <div className="max-w-4xl mx-auto space-y-6">
              
              {/* Quote Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Quote Summary</span>
                    <div className="text-2xl font-bold text-gray-900">
                      {formatCurrency(quote.total, quote.currency)}
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">Customer Information</h4>
                      <div className="space-y-2 text-sm">
                        <div><strong>Name:</strong> {quote.customerName}</div>
                        <div><strong>Email:</strong> {quote.customerEmail}</div>
                        {quote.customerPhone && <div><strong>Phone:</strong> {quote.customerPhone}</div>}
                        {quote.customerCompany && <div><strong>Company:</strong> {quote.customerCompany}</div>}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-gray-900 mb-3">Quote Details</h4>
                      <div className="space-y-2 text-sm">
                        <div><strong>Created:</strong> {formatDate(quote.createdAt)}</div>
                        <div><strong>Updated:</strong> {formatDate(quote.updatedAt)}</div>
                        <div><strong>Valid Until:</strong> {formatDate(quote.validUntil)}</div>
                        <div><strong>Items:</strong> {quote.items.length} item{quote.items.length !== 1 ? 's' : ''}</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quote Items */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Package className="w-5 h-5 mr-2" />
                    Quote Items
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {quote.items.map((item, index) => (
                      <div key={item.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{item.product.name}</h4>
                          {item.product.sku && (
                            <p className="text-sm text-gray-600">SKU: {item.product.sku}</p>
                          )}
                          {item.variant && (
                            <p className="text-sm text-gray-600">Variant: {item.variant.name}</p>
                          )}
                          {item.notes && (
                            <p className="text-sm text-gray-600 mt-1">Notes: {item.notes}</p>
                          )}
                        </div>
                        
                        <div className="text-right">
                          <div className="text-sm text-gray-600">
                            {item.quantity} × {formatCurrency(item.unitPrice, quote.currency)}
                          </div>
                          <div className="font-medium text-gray-900">
                            {formatCurrency(item.totalPrice, quote.currency)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* Pricing Breakdown */}
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Subtotal:</span>
                        <span>{formatCurrency(quote.subtotal, quote.currency)}</span>
                      </div>
                      {quote.tax > 0 && (
                        <div className="flex justify-between text-sm">
                          <span>Tax:</span>
                          <span>{formatCurrency(quote.tax, quote.currency)}</span>
                        </div>
                      )}
                      {quote.shipping > 0 && (
                        <div className="flex justify-between text-sm">
                          <span>Shipping:</span>
                          <span>{formatCurrency(quote.shipping, quote.currency)}</span>
                        </div>
                      )}
                      <div className="flex justify-between text-lg font-semibold pt-2 border-t">
                        <span>Total:</span>
                        <span>{formatCurrency(quote.total, quote.currency)}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Order Conversion Status */}
              {quote.order && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center text-green-600">
                      <CheckCircle className="w-5 h-5 mr-2" />
                      Converted to Order
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      This quote has been converted to order{' '}
                      <Link 
                        href={`/dashboard/orders/${quote.order.id}`}
                        className="text-blue-600 hover:text-blue-700 font-medium"
                      >
                        {quote.order.orderNumber}
                      </Link>
                      {' '}with status: <Badge>{quote.order.status}</Badge>
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
