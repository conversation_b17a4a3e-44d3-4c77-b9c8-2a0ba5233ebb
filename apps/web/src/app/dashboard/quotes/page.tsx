'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/auth-context';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../components/dashboard/dashboard-sidebar';
import { Button } from '../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Input } from '../../../components/ui/input';
import { Badge } from '../../../components/ui/badge';
import { 
  Plus, 
  Search, 
  Filter,
  Eye,
  Edit,
  Trash2,
  FileText,
  Calendar,
  DollarSign,
  User,
  Building2,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import Link from 'next/link';

// Force dynamic rendering to avoid prerendering issues with client components
export const dynamic = 'force-dynamic';

interface Quote {
  id: string;
  quoteNumber: string;
  status: 'PENDING' | 'SENT' | 'ACCEPTED' | 'REJECTED' | 'EXPIRED' | 'CONVERTED';
  total: number;
  currency: string;
  customerName: string;
  customerEmail: string;
  customerCompany?: string;
  validUntil: string;
  createdAt: string;
  items: Array<{
    id: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    product: {
      id: string;
      name: string;
      sku?: string;
    };
  }>;
  order?: {
    id: string;
    orderNumber: string;
    status: string;
  };
}

const statusColors = {
  PENDING: 'bg-yellow-100 text-yellow-800',
  SENT: 'bg-blue-100 text-blue-800',
  ACCEPTED: 'bg-green-100 text-green-800',
  REJECTED: 'bg-red-100 text-red-800',
  EXPIRED: 'bg-gray-100 text-gray-800',
  CONVERTED: 'bg-purple-100 text-purple-800',
};

const statusIcons = {
  PENDING: AlertCircle,
  SENT: FileText,
  ACCEPTED: CheckCircle,
  REJECTED: AlertCircle,
  EXPIRED: Calendar,
  CONVERTED: CheckCircle,
};

export default function QuotesPage() {
  const { user } = useAuth();
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user?.factoryId) {
      loadQuotes();
    }
  }, [user?.factoryId, searchQuery, statusFilter]);

  const loadQuotes = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        factoryId: user!.factoryId!,
        page: '1',
        limit: '50'
      });

      if (searchQuery) {
        params.append('query', searchQuery);
      }

      if (statusFilter) {
        params.append('status', statusFilter);
      }

      const response = await fetch(`/api/quotes?${params}`);
      
      if (response.ok) {
        const data = await response.json();
        setQuotes(data.quotes);
        console.log('✅ Quotes loaded:', data.quotes.length);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to load quotes');
      }
    } catch (error) {
      console.error('❌ Error loading quotes:', error);
      setError('Failed to load quotes. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteQuote = async (quoteId: string, quoteNumber: string) => {
    if (!confirm(`Are you sure you want to delete quote ${quoteNumber}?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/quotes/${quoteId}?factoryId=${user!.factoryId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setQuotes(quotes.filter(q => q.id !== quoteId));
        console.log('✅ Quote deleted successfully');
      } else {
        const errorData = await response.json();
        alert(`Error deleting quote: ${errorData.error}`);
      }
    } catch (error) {
      console.error('❌ Error deleting quote:', error);
      alert('Failed to delete quote. Please try again.');
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const isExpired = (validUntil: string) => {
    return new Date(validUntil) < new Date();
  };

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Quote Management</h1>
                <p className="text-gray-600">Manage customer quotes and convert them to orders</p>
              </div>
              
              <Button asChild className="bg-blue-600 hover:bg-blue-700">
                <Link href="/dashboard/quotes/create">
                  <Plus className="w-4 h-4 mr-2" />
                  Create Quote
                </Link>
              </Button>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search quotes by number, customer, or company..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Filter className="w-4 h-4 text-gray-400" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">All Status</option>
                  <option value="PENDING">Pending</option>
                  <option value="SENT">Sent</option>
                  <option value="ACCEPTED">Accepted</option>
                  <option value="REJECTED">Rejected</option>
                  <option value="EXPIRED">Expired</option>
                  <option value="CONVERTED">Converted</option>
                </select>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center">
                  <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                  <p className="text-red-800">{error}</p>
                </div>
              </div>
            )}

            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-gray-500">Loading quotes...</div>
              </div>
            ) : quotes.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No quotes found</h3>
                <p className="text-gray-600 mb-6">
                  {searchQuery || statusFilter 
                    ? 'No quotes match your current filters.' 
                    : 'Get started by creating your first quote.'}
                </p>
                <Button asChild>
                  <Link href="/dashboard/quotes/create">
                    <Plus className="w-4 h-4 mr-2" />
                    Create Quote
                  </Link>
                </Button>
              </div>
            ) : (
              <div className="grid gap-6">
                {quotes.map((quote) => {
                  const StatusIcon = statusIcons[quote.status];
                  const expired = isExpired(quote.validUntil);
                  
                  return (
                    <Card key={quote.id} className={`${expired && quote.status === 'PENDING' ? 'border-red-200' : ''}`}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div>
                              <CardTitle className="text-lg">{quote.quoteNumber}</CardTitle>
                              <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                                <div className="flex items-center">
                                  <User className="w-4 h-4 mr-1" />
                                  {quote.customerName}
                                </div>
                                {quote.customerCompany && (
                                  <div className="flex items-center">
                                    <Building2 className="w-4 h-4 mr-1" />
                                    {quote.customerCompany}
                                  </div>
                                )}
                                <div className="flex items-center">
                                  <Calendar className="w-4 h-4 mr-1" />
                                  Valid until {formatDate(quote.validUntil)}
                                  {expired && <span className="text-red-600 ml-1">(Expired)</span>}
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-3">
                            <Badge className={statusColors[quote.status]}>
                              <StatusIcon className="w-3 h-3 mr-1" />
                              {quote.status}
                            </Badge>
                            <div className="text-right">
                              <div className="text-lg font-semibold text-gray-900">
                                {formatCurrency(quote.total, quote.currency)}
                              </div>
                              <div className="text-sm text-gray-600">
                                {quote.items.length} item{quote.items.length !== 1 ? 's' : ''}
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                      
                      <CardContent>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <span>Created {formatDate(quote.createdAt)}</span>
                            {quote.order && (
                              <span className="text-green-600">
                                → Converted to Order {quote.order.orderNumber}
                              </span>
                            )}
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Button variant="outline" size="sm" asChild>
                              <Link href={`/dashboard/quotes/${quote.id}`}>
                                <Eye className="w-4 h-4 mr-1" />
                                View
                              </Link>
                            </Button>
                            
                            {quote.status !== 'CONVERTED' && (
                              <>
                                <Button variant="outline" size="sm" asChild>
                                  <Link href={`/dashboard/quotes/${quote.id}/edit`}>
                                    <Edit className="w-4 h-4 mr-1" />
                                    Edit
                                  </Link>
                                </Button>
                                
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => handleDeleteQuote(quote.id, quote.quoteNumber)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="w-4 h-4 mr-1" />
                                  Delete
                                </Button>
                              </>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
