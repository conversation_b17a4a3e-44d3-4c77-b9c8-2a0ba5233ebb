'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../../contexts/auth-context';
import { ProtectedRoute } from '../../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../../components/dashboard/dashboard-sidebar';
import { Button } from '../../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Input } from '../../../../components/ui/input';
import { Label } from '../../../../components/ui/label';
import { Textarea } from '../../../../components/ui/textarea';
import { 
  ArrowLeft, 
  Save, 
  Plus,
  Trash2,
  AlertCircle,
  CheckCircle,
  Search
} from 'lucide-react';
import Link from 'next/link';

// Force dynamic rendering to avoid prerendering issues with client components
export const dynamic = 'force-dynamic';

interface Product {
  id: string;
  name: string;
  sku?: string;
  basePrice: number;
  currency: string;
  minOrderQty: number;
}

interface QuoteItem {
  productId: string;
  product?: Product;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  notes?: string;
}

export default function CreateQuotePage() {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [productsLoading, setProductsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    customerCompany: '',
    validityDays: 30,
    tax: 0,
    shipping: 0,
    currency: 'USD',
    shippingMethod: '',
    notes: '',
  });

  const [items, setItems] = useState<QuoteItem[]>([
    { productId: '', quantity: 1, unitPrice: 0, totalPrice: 0 }
  ]);

  // Load products on component mount
  useEffect(() => {
    const loadProducts = async () => {
      if (!user?.factoryId) return;

      setProductsLoading(true);
      try {
        const response = await fetch(`/api/products?factoryId=${user.factoryId}&limit=100`);
        if (response.ok) {
          const data = await response.json();
          setProducts(data.products);
          console.log('✅ Products loaded:', data.products.length);
        } else {
          console.error('❌ Failed to load products');
          setError('Failed to load products. Please refresh the page.');
        }
      } catch (error) {
        console.error('❌ Error loading products:', error);
        setError('Failed to load products. Please refresh the page.');
      } finally {
        setProductsLoading(false);
      }
    };

    loadProducts();
  }, [user?.factoryId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (error) setError(null);
  };

  const handleItemChange = (index: number, field: keyof QuoteItem, value: any) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], [field]: value };

    // If product is selected, update unit price and calculate total
    if (field === 'productId' && value) {
      const product = products.find(p => p.id === value);
      if (product) {
        newItems[index].product = product;
        newItems[index].unitPrice = product.basePrice;
        newItems[index].quantity = Math.max(newItems[index].quantity, product.minOrderQty);
      }
    }

    // Calculate total price
    if (field === 'quantity' || field === 'unitPrice' || field === 'productId') {
      newItems[index].totalPrice = newItems[index].quantity * newItems[index].unitPrice;
    }

    setItems(newItems);
  };

  const addItem = () => {
    setItems([...items, { productId: '', quantity: 1, unitPrice: 0, totalPrice: 0 }]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
  };

  const calculateSubtotal = () => {
    return items.reduce((sum, item) => sum + item.totalPrice, 0);
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    return subtotal + formData.tax + formData.shipping;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user?.factoryId) {
      setError('No factory ID found. Please ensure you are logged in.');
      return;
    }

    // Validate required fields
    if (!formData.customerName || !formData.customerEmail) {
      setError('Please fill in customer name and email.');
      return;
    }

    // Validate items
    const validItems = items.filter(item => item.productId && item.quantity > 0 && item.unitPrice > 0);
    if (validItems.length === 0) {
      setError('Please add at least one valid item to the quote.');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);
    
    // Retry logic for quote creation
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        console.log(`🔄 Creating quote (attempt ${attempt}/3):`, {
          customerName: formData.customerName,
          itemsCount: validItems.length,
          factoryId: user.factoryId
        });

        const response = await fetch('/api/quotes', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...formData,
            factoryId: user.factoryId,
            items: validItems,
            tax: parseFloat(formData.tax.toString()),
            shipping: parseFloat(formData.shipping.toString()),
          }),
        });

        if (response.ok) {
          const quote = await response.json();
          console.log('✅ Quote created successfully:', quote.id);
          setSuccess('Quote created successfully! Redirecting...');
          
          // Redirect after a short delay to show success message
          setTimeout(() => {
            router.push('/dashboard/quotes');
          }, 1500);
          return;
        } else {
          const errorData = await response.json();
          console.error(`❌ Quote creation failed (attempt ${attempt}/3):`, response.status, errorData);
          
          if (response.status === 500 && attempt < 3) {
            console.log(`🔄 Retrying quote creation in ${attempt}s...`);
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            continue;
          }
          
          setError(`Failed to create quote: ${errorData.error || 'Unknown error'}`);
          break;
        }
      } catch (error) {
        console.error(`❌ Quote creation error (attempt ${attempt}/3):`, error);
        
        if (attempt < 3) {
          console.log(`🔄 Retrying quote creation due to network error...`);
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          continue;
        }
        
        setError('Failed to create quote. Please check your connection and try again.');
      }
    }
    
    setLoading(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: formData.currency,
    }).format(amount);
  };

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/dashboard/quotes">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Quotes
                  </Link>
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Create New Quote</h1>
                  <p className="text-gray-600">Generate a quote for your customer</p>
                </div>
              </div>
              
              <Button
                type="submit"
                form="quote-form"
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Save className="w-4 h-4 mr-2" />
                {loading ? 'Creating...' : 'Create Quote'}
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            <div className="max-w-4xl mx-auto space-y-6">
              
              {/* Error and Success Messages */}
              {error && (
                <div className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="w-5 h-5 text-red-600" />
                  <p className="text-red-800">{error}</p>
                </div>
              )}
              
              {success && (
                <div className="flex items-center space-x-2 p-4 bg-green-50 border border-green-200 rounded-md">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <p className="text-green-800">{success}</p>
                </div>
              )}

              <form id="quote-form" onSubmit={handleSubmit} className="space-y-6">
                
                {/* Customer Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>Customer Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="customerName">Customer Name *</Label>
                        <Input
                          id="customerName"
                          name="customerName"
                          value={formData.customerName}
                          onChange={handleInputChange}
                          placeholder="Enter customer name"
                          required
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="customerEmail">Customer Email *</Label>
                        <Input
                          id="customerEmail"
                          name="customerEmail"
                          type="email"
                          value={formData.customerEmail}
                          onChange={handleInputChange}
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="customerPhone">Phone Number</Label>
                        <Input
                          id="customerPhone"
                          name="customerPhone"
                          value={formData.customerPhone}
                          onChange={handleInputChange}
                          placeholder="+****************"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="customerCompany">Company</Label>
                        <Input
                          id="customerCompany"
                          name="customerCompany"
                          value={formData.customerCompany}
                          onChange={handleInputChange}
                          placeholder="Company name"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Quote Items */}
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle>Quote Items</CardTitle>
                      <Button type="button" onClick={addItem} variant="outline" size="sm">
                        <Plus className="w-4 h-4 mr-2" />
                        Add Item
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {productsLoading ? (
                      <div className="text-center py-8 text-gray-500">Loading products...</div>
                    ) : (
                      <div className="space-y-4">
                        {items.map((item, index) => (
                          <div key={index} className="grid grid-cols-12 gap-4 items-end p-4 border border-gray-200 rounded-lg">
                            <div className="col-span-4">
                              <Label>Product *</Label>
                              <select
                                value={item.productId}
                                onChange={(e) => handleItemChange(index, 'productId', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                              >
                                <option value="">Select a product</option>
                                {products.map((product) => (
                                  <option key={product.id} value={product.id}>
                                    {product.name} {product.sku && `(${product.sku})`}
                                  </option>
                                ))}
                              </select>
                            </div>
                            
                            <div className="col-span-2">
                              <Label>Quantity *</Label>
                              <Input
                                type="number"
                                min="1"
                                value={item.quantity}
                                onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value) || 1)}
                                required
                              />
                            </div>
                            
                            <div className="col-span-2">
                              <Label>Unit Price *</Label>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                value={item.unitPrice}
                                onChange={(e) => handleItemChange(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                                required
                              />
                            </div>
                            
                            <div className="col-span-2">
                              <Label>Total</Label>
                              <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md">
                                {formatCurrency(item.totalPrice)}
                              </div>
                            </div>
                            
                            <div className="col-span-2">
                              {items.length > 1 && (
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => removeItem(index)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Quote Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle>Quote Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="tax">Tax Amount</Label>
                        <Input
                          id="tax"
                          name="tax"
                          type="number"
                          step="0.01"
                          min="0"
                          value={formData.tax}
                          onChange={handleInputChange}
                          placeholder="0.00"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="shipping">Shipping Cost</Label>
                        <Input
                          id="shipping"
                          name="shipping"
                          type="number"
                          step="0.01"
                          min="0"
                          value={formData.shipping}
                          onChange={handleInputChange}
                          placeholder="0.00"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="validityDays">Valid for (days)</Label>
                        <Input
                          id="validityDays"
                          name="validityDays"
                          type="number"
                          min="1"
                          value={formData.validityDays}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <div className="flex justify-between items-center text-lg font-semibold">
                        <span>Total Quote Value:</span>
                        <span>{formatCurrency(calculateTotal())}</span>
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        Subtotal: {formatCurrency(calculateSubtotal())} + 
                        Tax: {formatCurrency(formData.tax)} + 
                        Shipping: {formatCurrency(formData.shipping)}
                      </div>
                    </div>
                  </CardContent>
                </Card>

              </form>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
