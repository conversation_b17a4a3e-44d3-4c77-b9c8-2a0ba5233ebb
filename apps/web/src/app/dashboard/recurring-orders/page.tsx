'use client';

import { useState } from 'react';
import { useAuth } from '../../../contexts/auth-context';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../components/dashboard/dashboard-sidebar';
import { But<PERSON> } from '../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Badge } from '../../../components/ui/badge';
import { Input } from '../../../components/ui/input';
import { Switch } from '../../../components/ui/switch';
import {
  Calendar,
  Clock,
  Play,
  Pause,
  Edit,
  Eye,
  Search,
  Filter,
  Plus,
  RefreshCw,
  User,
  Package,
  ShoppingCart,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import Link from 'next/link';
import { trpc } from '../../../lib/trpc';
import { RecurringFrequency } from '@fc-china/shared-types';

// Force dynamic rendering to avoid prerendering issues with client components
export const dynamic = 'force-dynamic';

interface RecurringOrder {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
  frequency: RecurringFrequency;
  intervalValue: number;
  startDate: string;
  endDate: string | null;
  nextRunDate: string;
  lastRunDate: string | null;
  customerName: string;
  customerEmail: string;
  customerPhone: string | null;
  customerCompany: string | null;
  autoApprove: boolean;
  notifyCustomer: boolean;
  notifyFactory: boolean;
  template: {
    id: string;
    name: string;
    templateType: string;
    estimatedTotalAmount: number | null;
    defaultCurrency: string;
  };
  generatedOrders?: Array<{
    id: string;
    orderNumber: string;
    status: string;
    totalAmount: number;
    createdAt: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

const frequencyLabels = {
  DAILY: 'Daily',
  WEEKLY: 'Weekly',
  BIWEEKLY: 'Bi-weekly',
  MONTHLY: 'Monthly',
  QUARTERLY: 'Quarterly',
  YEARLY: 'Yearly',
  CUSTOM: 'Custom',
};

const frequencyColors = {
  DAILY: 'bg-red-100 text-red-800',
  WEEKLY: 'bg-blue-100 text-blue-800',
  BIWEEKLY: 'bg-purple-100 text-purple-800',
  MONTHLY: 'bg-green-100 text-green-800',
  QUARTERLY: 'bg-orange-100 text-orange-800',
  YEARLY: 'bg-gray-100 text-gray-800',
  CUSTOM: 'bg-yellow-100 text-yellow-800',
};

export default function RecurringOrdersPage() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFrequency, setSelectedFrequency] = useState<RecurringFrequency | ''>('');
  const [selectedStatus, setSelectedStatus] = useState<'active' | 'inactive' | ''>('');

  // Fetch recurring orders
  const { data: recurringOrdersData, isLoading, refetch } = trpc.recurringOrders.getAll.useQuery({
    page: 1,
    limit: 50,
    search: searchQuery || undefined,
    frequency: selectedFrequency || undefined,
    isActive: selectedStatus === 'active' ? true : selectedStatus === 'inactive' ? false : undefined,
  });

  const recurringOrders = recurringOrdersData?.data || [];
  const pagination = recurringOrdersData?.pagination;

  const toggleActive = trpc.recurringOrders.toggleActive.useMutation({
    onSuccess: () => {
      refetch();
    },
    onError: (error) => {
      console.error('Failed to toggle recurring order status:', error);
      alert('Failed to update status. Please try again.');
    },
  });

  const handleToggleActive = async (id: string, isActive: boolean) => {
    try {
      await toggleActive.mutateAsync({ id, isActive: !isActive });
    } catch (error) {
      console.error('Error toggling recurring order:', error);
    }
  };

  const getStatusIcon = (isActive: boolean, nextRunDate: string) => {
    if (!isActive) {
      return <XCircle className="w-4 h-4 text-red-500" />;
    }
    
    const nextRun = new Date(nextRunDate);
    const now = new Date();
    const isOverdue = nextRun < now;
    
    if (isOverdue) {
      return <AlertCircle className="w-4 h-4 text-orange-500" />;
    }
    
    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  const getStatusText = (isActive: boolean, nextRunDate: string) => {
    if (!isActive) return 'Inactive';
    
    const nextRun = new Date(nextRunDate);
    const now = new Date();
    const isOverdue = nextRun < now;
    
    if (isOverdue) return 'Overdue';
    return 'Active';
  };

  const formatNextRun = (nextRunDate: string) => {
    const date = new Date(nextRunDate);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return `Overdue by ${Math.abs(diffDays)} day${Math.abs(diffDays) !== 1 ? 's' : ''}`;
    } else if (diffDays === 0) {
      return 'Due today';
    } else if (diffDays === 1) {
      return 'Due tomorrow';
    } else {
      return `Due in ${diffDays} days`;
    }
  };

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Recurring Orders</h1>
                <p className="text-gray-600">Manage automated order schedules</p>
              </div>
              
              <div className="flex items-center space-x-3">
                <Button
                  onClick={() => refetch()}
                  variant="outline"
                  disabled={isLoading}
                >
                  <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                <Link href="/dashboard/templates">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="w-4 h-4 mr-2" />
                    Create from Template
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search recurring orders..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Frequency Filter */}
              <select
                value={selectedFrequency}
                onChange={(e) => setSelectedFrequency(e.target.value as RecurringFrequency | '')}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Frequencies</option>
                <option value="DAILY">Daily</option>
                <option value="WEEKLY">Weekly</option>
                <option value="BIWEEKLY">Bi-weekly</option>
                <option value="MONTHLY">Monthly</option>
                <option value="QUARTERLY">Quarterly</option>
                <option value="YEARLY">Yearly</option>
                <option value="CUSTOM">Custom</option>
              </select>

              {/* Status Filter */}
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value as 'active' | 'inactive' | '')}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : recurringOrders.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    No recurring orders found
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Create your first recurring order from a template to automate your order process.
                  </p>
                  <Link href="/dashboard/templates">
                    <Button>
                      <Plus className="w-4 h-4 mr-2" />
                      Browse Templates
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {recurringOrders.map((recurringOrder: RecurringOrder) => (
                  <RecurringOrderCard
                    key={recurringOrder.id}
                    recurringOrder={recurringOrder}
                    onToggleActive={handleToggleActive}
                    isToggling={toggleActive.isPending}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

function RecurringOrderCard({
  recurringOrder,
  onToggleActive,
  isToggling
}: {
  recurringOrder: RecurringOrder;
  onToggleActive: (id: string, isActive: boolean) => void;
  isToggling: boolean;
}) {
  const getStatusIcon = (isActive: boolean, nextRunDate: string) => {
    if (!isActive) {
      return <XCircle className="w-4 h-4 text-red-500" />;
    }

    const nextRun = new Date(nextRunDate);
    const now = new Date();
    const isOverdue = nextRun < now;

    if (isOverdue) {
      return <AlertCircle className="w-4 h-4 text-orange-500" />;
    }

    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  const getStatusText = (isActive: boolean, nextRunDate: string) => {
    if (!isActive) return 'Inactive';

    const nextRun = new Date(nextRunDate);
    const now = new Date();
    const isOverdue = nextRun < now;

    if (isOverdue) return 'Overdue';
    return 'Active';
  };

  const formatNextRun = (nextRunDate: string) => {
    const date = new Date(nextRunDate);
    const now = new Date();
    const diffTime = date.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return `Overdue by ${Math.abs(diffDays)} day${Math.abs(diffDays) !== 1 ? 's' : ''}`;
    } else if (diffDays === 0) {
      return 'Due today';
    } else if (diffDays === 1) {
      return 'Due tomorrow';
    } else {
      return `Due in ${diffDays} days`;
    }
  };

  const formatFrequency = (frequency: RecurringFrequency, intervalValue: number) => {
    const label = frequencyLabels[frequency];
    if (intervalValue === 1) {
      return label;
    }
    return `Every ${intervalValue} ${label.toLowerCase()}`;
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          {/* Main Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {recurringOrder.name}
              </h3>
              <div className="flex items-center space-x-2">
                {getStatusIcon(recurringOrder.isActive, recurringOrder.nextRunDate)}
                <span className="text-sm font-medium">
                  {getStatusText(recurringOrder.isActive, recurringOrder.nextRunDate)}
                </span>
              </div>
            </div>

            {recurringOrder.description && (
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                {recurringOrder.description}
              </p>
            )}

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              {/* Schedule Info */}
              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="w-4 h-4 mr-2" />
                  <span>Schedule</span>
                </div>
                <div>
                  <Badge className={frequencyColors[recurringOrder.frequency]}>
                    {formatFrequency(recurringOrder.frequency, recurringOrder.intervalValue)}
                  </Badge>
                </div>
                <div className="text-sm text-gray-600">
                  {formatNextRun(recurringOrder.nextRunDate)}
                </div>
              </div>

              {/* Customer Info */}
              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-600">
                  <User className="w-4 h-4 mr-2" />
                  <span>Customer</span>
                </div>
                <div className="text-sm">
                  <div className="font-medium text-gray-900">
                    {recurringOrder.customerName}
                  </div>
                  {recurringOrder.customerCompany && (
                    <div className="text-gray-600">
                      {recurringOrder.customerCompany}
                    </div>
                  )}
                  <div className="text-gray-600">
                    {recurringOrder.customerEmail}
                  </div>
                </div>
              </div>

              {/* Template Info */}
              <div className="space-y-2">
                <div className="flex items-center text-sm text-gray-600">
                  <Package className="w-4 h-4 mr-2" />
                  <span>Template</span>
                </div>
                <div className="text-sm">
                  <div className="font-medium text-gray-900">
                    {recurringOrder.template.name}
                  </div>
                  <div className="text-gray-600">
                    {recurringOrder.template.estimatedTotalAmount
                      ? `Est. ${recurringOrder.template.defaultCurrency} ${recurringOrder.template.estimatedTotalAmount.toFixed(2)}`
                      : 'Template configured'
                    }
                  </div>
                </div>
              </div>
            </div>

            {/* Generated Orders Summary */}
            {recurringOrder.generatedOrders && recurringOrder.generatedOrders.length > 0 && (
              <div className="border-t pt-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center text-sm text-gray-600">
                    <ShoppingCart className="w-4 h-4 mr-2" />
                    <span>Recent Orders ({recurringOrder.generatedOrders.length})</span>
                  </div>
                </div>
                <div className="flex flex-wrap gap-2">
                  {recurringOrder.generatedOrders.slice(0, 3).map((order) => (
                    <Link
                      key={order.id}
                      href={`/dashboard/orders/${order.id}`}
                      className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded transition-colors"
                    >
                      {order.orderNumber}
                    </Link>
                  ))}
                  {recurringOrder.generatedOrders.length > 3 && (
                    <span className="text-xs text-gray-500 px-2 py-1">
                      +{recurringOrder.generatedOrders.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2 ml-4">
            <div className="flex items-center space-x-2">
              <Switch
                checked={recurringOrder.isActive}
                onCheckedChange={() => onToggleActive(recurringOrder.id, recurringOrder.isActive)}
                disabled={isToggling}
              />
              <span className="text-sm text-gray-600">
                {recurringOrder.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>

            <Link href={`/dashboard/recurring-orders/${recurringOrder.id}`}>
              <Button variant="outline" size="sm">
                <Eye className="w-4 h-4 mr-2" />
                View
              </Button>
            </Link>

            <Link href={`/dashboard/recurring-orders/${recurringOrder.id}/edit`}>
              <Button variant="outline" size="sm">
                <Edit className="w-4 h-4 mr-2" />
                Edit
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
