'use client';

import { useState, useMemo } from 'react';
import { useAuth } from '../../../contexts/auth-context';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../components/dashboard/dashboard-sidebar';
import { But<PERSON> } from '../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Input } from '../../../components/ui/input';
import { Badge } from '../../../components/ui/badge';
import {
  Search,
  Filter,
  Grid3X3,
  List,
  BookOpen,
  Eye,
  Copy,
  Plus,
  Calendar,
  DollarSign,
  Package,
  Settings
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/ui/select';
import Link from 'next/link';
import { trpc } from '../../../lib/trpc';
// Define TemplateType locally since we're using the tRPC types
type TemplateType = 'STANDARD' | 'RECURRING' | 'CUSTOMER' | 'SEASONAL';

// Define a simplified template type to avoid deep type inference issues
type SimpleTemplate = {
  id: string;
  name: string;
  description?: string | null;
  templateType: TemplateType;
  isActive: boolean;
  isPublic: boolean;
  estimatedTotalAmount?: number | null;
  createdAt: string;
  updatedAt: string;
  items: any[];
  createdByUser?: any;
  _count?: any;
};

// Remove the PublicTemplate interface - we'll use the factory's own templates

// Force dynamic rendering to avoid prerendering issues with client components
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

const templateTypeColors = {
  STANDARD: 'bg-blue-100 text-blue-800',
  RECURRING: 'bg-green-100 text-green-800',
  CUSTOMER: 'bg-purple-100 text-purple-800',
  SEASONAL: 'bg-orange-100 text-orange-800',
};

const templateTypeLabels = {
  STANDARD: 'Standard',
  RECURRING: 'Recurring',
  CUSTOMER: 'Customer',
  SEASONAL: 'Seasonal',
};

const getStatusColor = (isActive: boolean) => {
  return isActive
    ? 'bg-green-100 text-green-800'
    : 'bg-gray-100 text-gray-800';
};

export default function TemplateLibraryPage() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<TemplateType | ''>('');
  const [showActiveOnly, setShowActiveOnly] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Fetch factory's own templates using existing tRPC procedure
  const { data: templatesData, isLoading, refetch } = trpc.orderTemplates.getAll.useQuery({
    page: 1,
    limit: 50,
    templateType: selectedType || undefined,
    isActive: showActiveOnly ? true : undefined,
    search: searchQuery || undefined,
  });

  const templates = templatesData?.data || [];

  // Filter templates based on search and type - cast to simplified type to avoid deep inference
  const filteredTemplates = (templates as SimpleTemplate[]).filter((template) => {
    const matchesSearch = !searchQuery ||
      template.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description?.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesSearch;
  });

  const getStatusColor = (isActive: boolean) => {
    return isActive
      ? 'bg-green-100 text-green-800'
      : 'bg-gray-100 text-gray-800';
  };

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <BookOpen className="w-6 h-6 mr-3 text-blue-600" />
                  Template Library
                </h1>
                <p className="text-gray-600">Manage and organize your factory's order templates</p>
              </div>

              <div className="flex items-center space-x-3">
                <Link href="/dashboard/templates/new">
                  <Button size="sm">
                    <Plus className="w-4 h-4 mr-2" />
                    New Template
                  </Button>
                </Link>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center space-x-4 flex-wrap gap-2">
              {/* Search */}
              <div className="relative flex-1 min-w-64">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search your templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Template Type Filter */}
              <Select value={selectedType || "all"} onValueChange={(value) => setSelectedType(value === "all" ? '' : value as TemplateType)}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="STANDARD">Standard</SelectItem>
                  <SelectItem value="RECURRING">Recurring</SelectItem>
                  <SelectItem value="CUSTOMER">Customer</SelectItem>
                  <SelectItem value="SEASONAL">Seasonal</SelectItem>
                </SelectContent>
              </Select>

              {/* Active Status Filter */}
              <Select value={showActiveOnly ? 'active' : 'all'} onValueChange={(value) => setShowActiveOnly(value === 'active')}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active Only</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Stats Bar */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 px-6 py-3">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-6">
                <div className="flex items-center text-gray-600">
                  <Package className="w-4 h-4 mr-2" />
                  <span>{filteredTemplates.length} templates</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Settings className="w-4 h-4 mr-2" />
                  <span>{filteredTemplates.filter(t => t.isActive).length} active</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Calendar className="w-4 h-4 mr-2" />
                  <span>{filteredTemplates.filter(t => t.templateType === 'RECURRING').length} recurring</span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <BookOpen className="w-4 h-4 text-blue-500" />
                <span className="text-gray-600">Your factory templates</span>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading templates...</p>
                </div>
              </div>
            ) : filteredTemplates.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {templates.length === 0 ? 'No templates yet' : 'No templates found'}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {templates.length === 0
                      ? 'Create your first template to get started with streamlined ordering.'
                      : 'Try adjusting your search criteria or filters.'
                    }
                  </p>
                  {templates.length === 0 ? (
                    <Link href="/dashboard/templates/new">
                      <Button>
                        <Plus className="w-4 h-4 mr-2" />
                        Create Template
                      </Button>
                    </Link>
                  ) : (
                    <Button onClick={() => {
                      setSearchQuery('');
                      setSelectedType('');
                      setShowActiveOnly(false);
                    }}>
                      Clear Filters
                    </Button>
                  )}
                </CardContent>
              </Card>
            ) : (
              <div className={viewMode === 'grid'
                ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                : 'space-y-4'
              }>
                {filteredTemplates.map(template => (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    viewMode={viewMode}
                    onRefetch={refetch}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

function TemplateCard({
  template,
  viewMode,
  onRefetch
}: {
  template: any; // Using the actual template type from tRPC
  viewMode: 'grid' | 'list';
  onRefetch: () => void;
}) {
  const handleUseTemplate = () => {
    // Navigate to create order from template
    window.location.href = `/dashboard/orders/new?templateId=${template.id}`;
  };

  const handleEditTemplate = () => {
    // Navigate to edit template
    window.location.href = `/dashboard/templates/${template.id}/edit`;
  };

  if (viewMode === 'list') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4 flex-1">
              {/* Template Info */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-2">
                  <h3 className="text-lg font-semibold text-gray-900 truncate">
                    {template.name}
                  </h3>
                  <Badge className={templateTypeColors[template.templateType as TemplateType] || 'bg-gray-100 text-gray-800'}>
                    {templateTypeLabels[template.templateType as TemplateType] || template.templateType}
                  </Badge>
                  <Badge className={getStatusColor(template.isActive)}>
                    {template.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>

                <p className="text-gray-600 text-sm mb-2 line-clamp-2">
                  {template.description || 'No description provided'}
                </p>

                <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                  <div className="flex items-center">
                    <Package className="w-4 h-4 mr-1" />
                    <span>{template.items?.length || 0} items</span>
                  </div>
                  <div className="flex items-center">
                    <DollarSign className="w-4 h-4 mr-1" />
                    <span>{template.defaultCurrency} {template.estimatedTotalAmount ? Number(template.estimatedTotalAmount).toFixed(2) : 'N/A'}</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    <span>{new Date(template.createdAt).toLocaleDateString()}</span>
                  </div>
                </div>

                {/* Items Preview */}
                {template.items && template.items.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {template.items.slice(0, 3).map((item: any, index: number) => (
                      <span key={index} className="text-xs bg-gray-100 px-2 py-1 rounded">
                        {item.product?.name || 'Product'}
                      </span>
                    ))}
                    {template.items.length > 3 && (
                      <span className="text-xs text-gray-500 px-2 py-1">
                        +{template.items.length - 3} more
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2 ml-4">
              <Link href={`/dashboard/templates/${template.id}`}>
                <Button variant="outline" size="sm">
                  <Eye className="w-4 h-4 mr-1" />
                  View
                </Button>
              </Link>

              <Button variant="outline" size="sm" onClick={handleEditTemplate}>
                <Settings className="w-4 h-4 mr-1" />
                Edit
              </Button>

              <Button size="sm" onClick={handleUseTemplate} className="bg-blue-600 hover:bg-blue-700">
                <Copy className="w-4 h-4 mr-1" />
                Use Template
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Grid view
  return (
    <Card className="hover:shadow-lg transition-all duration-200 group">
      <CardContent className="p-0">
        {/* Header */}
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center justify-between mb-2">
            <Badge className={templateTypeColors[template.templateType as TemplateType] || 'bg-gray-100 text-gray-800'}>
              {templateTypeLabels[template.templateType as TemplateType] || template.templateType}
            </Badge>
            <Badge className={getStatusColor(template.isActive)}>
              {template.isActive ? 'Active' : 'Inactive'}
            </Badge>
          </div>

          <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2 group-hover:text-blue-600 transition-colors">
            {template.name}
          </h3>

          <p className="text-sm text-gray-600 line-clamp-2 mb-3">
            {template.description || 'No description provided'}
          </p>
        </div>

        {/* Stats */}
        <div className="p-4 bg-gray-50">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center text-gray-600">
              <Package className="w-4 h-4 mr-1" />
              <span>{template.items?.length || 0} items</span>
            </div>
            <div className="flex items-center text-gray-600">
              <Calendar className="w-4 h-4 mr-1" />
              <span>{new Date(template.createdAt).toLocaleDateString()}</span>
            </div>
            <div className="flex items-center text-gray-600">
              <DollarSign className="w-4 h-4 mr-1" />
              <span>{template.defaultCurrency} {template.estimatedTotalAmount ? Number(template.estimatedTotalAmount).toFixed(2) : 'N/A'}</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="p-4 border-t border-gray-100">
          <div className="flex space-x-2">
            <Link href={`/dashboard/templates/${template.id}`} className="flex-1">
              <Button variant="outline" size="sm" className="w-full">
                <Eye className="w-4 h-4 mr-1" />
                View
              </Button>
            </Link>
            <Button
              size="sm"
              onClick={handleUseTemplate}
              className="flex-1 bg-blue-600 hover:bg-blue-700"
            >
              <Copy className="w-4 h-4 mr-1" />
              Use
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
