'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../../contexts/auth-context';
import { ProtectedRoute } from '../../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../../components/dashboard/dashboard-sidebar';
import { Button } from '../../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Input } from '../../../../components/ui/input';
import { Label } from '../../../../components/ui/label';
import { Textarea } from '../../../../components/ui/textarea';
import { ImageUpload } from '../../../../components/ui/image-upload';
import {
  ArrowLeft,
  Save,
  Upload,
  FileSpreadsheet,
  Plus,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import Link from 'next/link';

export function CreateProductClient() {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [showBulkUpload, setShowBulkUpload] = useState(false);
  const [categories, setCategories] = useState<Array<{ id: string; name: string; description?: string }>>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state for individual product creation
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    shortDescription: '',
    categoryId: '',
    basePrice: '',
    currency: 'USD',
    minOrderQty: '1',
    maxOrderQty: '',
    sku: '',
    model: '',
    brand: '',
    weight: '',
    materials: '',
    colors: '',
    tags: '',
    stockQuantity: '0',
    stockStatus: 'IN_STOCK',
    status: 'DRAFT',
    isFeatured: false,
  });

  // Images state
  const [images, setImages] = useState<string[]>([]);

  // Load categories on component mount
  useEffect(() => {
    const loadCategories = async () => {
      if (!user?.factoryId) return;

      setCategoriesLoading(true);
      try {
        const response = await fetch(`/api/categories?factoryId=${user.factoryId}`);
        if (response.ok) {
          const responseData = await response.json();
          // The API returns { categories: [...] }, so we need to extract the categories array
          const categoriesData = responseData.categories || [];
          setCategories(categoriesData);
          console.log('✅ Categories loaded:', categoriesData.length);
        } else {
          console.error('❌ Failed to load categories');
          setError('Failed to load categories. Please refresh the page.');
        }
      } catch (error) {
        console.error('❌ Error loading categories:', error);
        setError('Failed to load categories. Please refresh the page.');
      } finally {
        setCategoriesLoading(false);
      }
    };

    loadCategories();
  }, [user?.factoryId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));

    // Clear error when user starts typing
    if (error) setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.factoryId) {
      setError('No factory ID found. Please ensure you are logged in.');
      return;
    }

    // Validate required fields
    if (!formData.name || !formData.categoryId || !formData.basePrice) {
      setError('Please fill in all required fields: Product Name, Category, and Base Price.');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    // Retry logic for product creation
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        console.log(`🔄 Creating product (attempt ${attempt}/3):`, {
          name: formData.name,
          categoryId: formData.categoryId,
          factoryId: user.factoryId
        });

        const response = await fetch('/api/products', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...formData,
            factoryId: user.factoryId,
            basePrice: parseFloat(formData.basePrice),
            minOrderQty: parseInt(formData.minOrderQty),
            maxOrderQty: formData.maxOrderQty ? parseInt(formData.maxOrderQty) : null,
            weight: formData.weight ? parseFloat(formData.weight) : null,
            stockQuantity: parseInt(formData.stockQuantity),
            materials: formData.materials ? formData.materials.split(',').map(m => m.trim()) : [],
            colors: formData.colors ? formData.colors.split(',').map(c => c.trim()) : [],
            tags: formData.tags ? formData.tags.split(',').map(t => t.trim()) : [],
            images: images, // Add images to the request
          }),
        });

        if (response.ok) {
          const product = await response.json();
          console.log('✅ Product created successfully:', product.id);
          setSuccess('Product created successfully! Redirecting...');

          // Redirect after a short delay to show success message
          setTimeout(() => {
            router.push('/dashboard/products');
          }, 1500);
          return;
        } else {
          const errorData = await response.json();
          console.error(`❌ Product creation failed (attempt ${attempt}/3):`, response.status, errorData);

          if (response.status === 500 && attempt < 3) {
            console.log(`🔄 Retrying product creation in ${attempt}s...`);
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            continue;
          }

          setError(`Failed to create product: ${errorData.error || 'Unknown error'}`);
          break;
        }
      } catch (error) {
        console.error(`❌ Product creation error (attempt ${attempt}/3):`, error);

        if (attempt < 3) {
          console.log(`🔄 Retrying product creation due to network error...`);
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          continue;
        }

        setError('Failed to create product. Please check your connection and try again.');
      }
    }

    setLoading(false);
  };

  const handleBulkUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.csv')) {
      setError('Please select a CSV file');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Read file content
      const fileContent = await file.text();

      console.log('📄 Processing CSV file:', {
        fileName: file.name,
        fileSize: file.size,
        contentPreview: fileContent.substring(0, 200) + '...'
      });

      // Call bulk import API
      const response = await fetch('/api/products/bulk-import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          csvData: fileContent,
          options: {
            skipErrors: true,
            validateOnly: false,
            updateExisting: false,
            batchSize: 50,
          },
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Bulk import failed');
      }

      console.log('✅ Bulk import completed:', result);

      // Show success message with details
      const successMessage = `
        Bulk import completed successfully!

        📊 Results:
        • Total records: ${result.totalRecords}
        • Successfully imported: ${result.successCount}
        • Errors: ${result.errorCount}

        ${result.errors.length > 0 ? `
        ⚠️ Errors encountered:
        ${result.errors.slice(0, 5).map((err: any) => `• Row ${err.row}: ${err.message}`).join('\n')}
        ${result.errors.length > 5 ? `... and ${result.errors.length - 5} more errors` : ''}
        ` : ''}
      `;

      alert(successMessage);

      // Redirect to products list if successful
      if (result.successCount > 0) {
        window.location.href = '/dashboard/products';
      }

    } catch (error) {
      console.error('❌ Bulk upload error:', error);
      setError(error instanceof Error ? error.message : 'Failed to upload CSV file');
    } finally {
      setLoading(false);
      // Reset file input
      e.target.value = '';
    }
  };

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="sm" asChild>
                  <Link href="/dashboard/products">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Products
                  </Link>
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Add New Product</h1>
                  <p className="text-gray-600">Create a new product for your factory catalog</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setShowBulkUpload(!showBulkUpload)}
                >
                  <FileSpreadsheet className="w-4 h-4 mr-2" />
                  Bulk Upload CSV
                </Button>
                <Button
                  type="submit"
                  form="product-form"
                  disabled={loading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Creating...' : 'Create Product'}
                </Button>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            <div className="max-w-4xl mx-auto space-y-6">

              {/* Bulk Upload Section */}
              {showBulkUpload && (
                <Card className="border-blue-200 bg-blue-50">
                  <CardHeader>
                    <CardTitle className="flex items-center text-blue-900">
                      <FileSpreadsheet className="w-5 h-5 mr-2" />
                      Bulk Product Upload
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-blue-800">
                        Upload multiple products at once using a CSV file. This is perfect for factories with large product catalogs.
                      </p>

                      <div className="flex items-center space-x-4">
                        <div className="flex-1">
                          <Label htmlFor="csv-upload" className="block text-sm font-medium text-blue-900 mb-2">
                            Select CSV File
                          </Label>
                          <Input
                            id="csv-upload"
                            type="file"
                            accept=".csv"
                            onChange={handleBulkUpload}
                            className="border-blue-300 focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <Button
                            variant="outline"
                            className="mt-6"
                            onClick={() => {
                              const link = document.createElement('a');
                              link.href = '/templates/product-import-template.csv';
                              link.download = 'product-import-template.csv';
                              document.body.appendChild(link);
                              link.click();
                              document.body.removeChild(link);
                            }}
                          >
                            Download Template
                          </Button>
                        </div>
                      </div>

                      <div className="text-sm text-blue-700">
                        <p><strong>CSV Format Requirements:</strong></p>
                        <ul className="list-disc list-inside mt-1 space-y-1">
                          <li>Required columns: name, description, categoryId, basePrice</li>
                          <li>Optional columns: sku, model, brand, weight, stockQuantity</li>
                          <li>Use comma-separated values for materials, colors, and tags</li>
                          <li>Maximum 1000 products per upload</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Individual Product Form */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Plus className="w-5 h-5 mr-2" />
                    Product Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form id="product-form" onSubmit={handleSubmit} className="space-y-6">

                    {/* Error and Success Messages */}
                    {error && (
                      <div className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-md">
                        <AlertCircle className="w-5 h-5 text-red-600" />
                        <p className="text-red-800">{error}</p>
                      </div>
                    )}

                    {success && (
                      <div className="flex items-center space-x-2 p-4 bg-green-50 border border-green-200 rounded-md">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                        <p className="text-green-800">{success}</p>
                      </div>
                    )}

                    {/* Basic Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="name">Product Name *</Label>
                        <Input
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          placeholder="Enter product name"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="categoryId">Category *</Label>
                        {categoriesLoading ? (
                          <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50">
                            Loading categories...
                          </div>
                        ) : (
                          <select
                            id="categoryId"
                            name="categoryId"
                            value={formData.categoryId}
                            onChange={handleInputChange}
                            required
                            title="Product Category"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="">Select a category</option>
                            {Array.isArray(categories) && categories.map((category) => (
                              <option key={category.id} value={category.id}>
                                {category.name}
                              </option>
                            ))}
                          </select>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="sku">SKU</Label>
                        <Input
                          id="sku"
                          name="sku"
                          value={formData.sku}
                          onChange={handleInputChange}
                          placeholder="Product SKU"
                        />
                      </div>

                      <div>
                        <Label htmlFor="model">Model</Label>
                        <Input
                          id="model"
                          name="model"
                          value={formData.model}
                          onChange={handleInputChange}
                          placeholder="Product model"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="shortDescription">Short Description</Label>
                      <Input
                        id="shortDescription"
                        name="shortDescription"
                        value={formData.shortDescription}
                        onChange={handleInputChange}
                        placeholder="Brief product description (max 500 characters)"
                        maxLength={500}
                      />
                    </div>

                    <div>
                      <Label htmlFor="description">Full Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        placeholder="Detailed product description"
                        rows={4}
                      />
                    </div>

                    {/* Pricing and Inventory */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <Label htmlFor="basePrice">Base Price *</Label>
                        <Input
                          id="basePrice"
                          name="basePrice"
                          type="number"
                          step="0.01"
                          value={formData.basePrice}
                          onChange={handleInputChange}
                          placeholder="0.00"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="minOrderQty">Minimum Order Quantity *</Label>
                        <Input
                          id="minOrderQty"
                          name="minOrderQty"
                          type="number"
                          value={formData.minOrderQty}
                          onChange={handleInputChange}
                          placeholder="1"
                          required
                        />
                      </div>

                      <div>
                        <Label htmlFor="stockQuantity">Stock Quantity</Label>
                        <Input
                          id="stockQuantity"
                          name="stockQuantity"
                          type="number"
                          value={formData.stockQuantity}
                          onChange={handleInputChange}
                          placeholder="0"
                        />
                      </div>
                    </div>

                    {/* Product Details */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="brand">Brand</Label>
                        <Input
                          id="brand"
                          name="brand"
                          value={formData.brand}
                          onChange={handleInputChange}
                          placeholder="Product brand"
                        />
                      </div>

                      <div>
                        <Label htmlFor="weight">Weight (kg)</Label>
                        <Input
                          id="weight"
                          name="weight"
                          type="number"
                          step="0.001"
                          value={formData.weight}
                          onChange={handleInputChange}
                          placeholder="0.000"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="materials">Materials (comma-separated)</Label>
                        <Input
                          id="materials"
                          name="materials"
                          value={formData.materials}
                          onChange={handleInputChange}
                          placeholder="Steel, Plastic, Aluminum"
                        />
                      </div>

                      <div>
                        <Label htmlFor="colors">Colors (comma-separated)</Label>
                        <Input
                          id="colors"
                          name="colors"
                          value={formData.colors}
                          onChange={handleInputChange}
                          placeholder="Red, Blue, Black"
                        />
                      </div>
                    </div>

                    {/* Status and Settings */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="status">Status</Label>
                        <select
                          id="status"
                          name="status"
                          value={formData.status}
                          onChange={handleInputChange}
                          title="Product Status"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="DRAFT">Draft</option>
                          <option value="ACTIVE">Active</option>
                          <option value="INACTIVE">Inactive</option>
                        </select>
                      </div>

                      <div>
                        <Label htmlFor="stockStatus">Stock Status</Label>
                        <select
                          id="stockStatus"
                          name="stockStatus"
                          value={formData.stockStatus}
                          onChange={handleInputChange}
                          title="Stock Status"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <option value="IN_STOCK">In Stock</option>
                          <option value="LOW_STOCK">Low Stock</option>
                          <option value="OUT_OF_STOCK">Out of Stock</option>
                          <option value="BACKORDER">Backorder</option>
                        </select>
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="tags">Tags (comma-separated)</Label>
                      <Input
                        id="tags"
                        name="tags"
                        value={formData.tags}
                        onChange={handleInputChange}
                        placeholder="electronics, industrial, premium"
                      />
                    </div>

                    {/* Product Images */}
                    <ImageUpload
                      images={images}
                      onImagesChange={setImages}
                      maxImages={5}
                      disabled={loading}
                    />

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="isFeatured"
                        name="isFeatured"
                        checked={formData.isFeatured}
                        onChange={handleInputChange}
                        title="Mark as Featured Product"
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <Label htmlFor="isFeatured">Featured Product</Label>
                    </div>

                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
