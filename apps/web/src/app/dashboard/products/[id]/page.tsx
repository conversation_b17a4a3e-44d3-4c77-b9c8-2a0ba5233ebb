'use client';

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '../../../../contexts/auth-context';
import { ProtectedRoute } from '../../../../components/auth/protected-route';
import { Card, CardContent, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Button } from '../../../../components/ui/button';
import { Badge } from '../../../../components/ui/badge';
import {
  ArrowLeft,
  Edit,
  Trash2,
  DollarSign,
  Package,
  BarChart3,
  Calendar,
  User,
  Tag,
  FileText,
  Image as ImageIcon
} from 'lucide-react';
import Link from 'next/link';
import { ProfessionalImageManager } from '../../../../components/product/professional-image-manager';

// Force dynamic rendering to avoid prerendering issues with client components
export const dynamic = 'force-dynamic';

interface Product {
  id: string;
  name: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  basePrice: number;
  currency: string;
  minOrderQty: number;
  sku?: string;
  status: 'DRAFT' | 'ACTIVE' | 'INACTIVE' | 'OUT_OF_STOCK' | 'DISCONTINUED';
  isFeatured: boolean;
  stockQuantity: number;
  stockStatus: string;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  images: any[];
  createdAt: string;
  updatedAt: string;
  factoryId: string;
}

export default function ProductDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const productId = params.id as string;

  useEffect(() => {
    const loadProduct = async () => {
      if (!user?.factoryId) {
        setError('No factory ID found');
        setLoading(false);
        return;
      }

      try {
        const response = await fetch(`/api/products/${productId}?factoryId=${user.factoryId}`);
        
        if (response.ok) {
          const productData = await response.json();
          setProduct(productData);
        } else {
          setError('Product not found');
        }
      } catch (error) {
        console.error('Error loading product:', error);
        setError('Failed to load product');
      } finally {
        setLoading(false);
      }
    };

    if (user?.factoryId) {
      loadProduct();
    }
  }, [user?.factoryId, productId]);

  const handleDelete = async () => {
    if (!product || !confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ factoryId: user?.factoryId }),
      });

      if (response.ok) {
        router.push('/dashboard/products');
      } else {
        alert('Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      alert('Failed to delete product');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800';
      case 'DRAFT': return 'bg-yellow-100 text-yellow-800';
      case 'INACTIVE': return 'bg-gray-100 text-gray-800';
      case 'OUT_OF_STOCK': return 'bg-red-100 text-red-800';
      case 'DISCONTINUED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case 'IN_STOCK': return 'bg-green-100 text-green-800';
      case 'LOW_STOCK': return 'bg-yellow-100 text-yellow-800';
      case 'OUT_OF_STOCK': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading product...</div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="text-lg text-red-600 mb-4">{error || 'Product not found'}</div>
            <Link href="/dashboard/products">
              <Button>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Products
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/products">
            <Button variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Products
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{product.name}</h1>
            <p className="text-gray-600">{product.sku}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Link href={`/dashboard/products/${product.id}/edit`}>
            <Button variant="outline">
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
          </Link>
          <Button 
            variant="outline" 
            className="text-red-600 hover:text-red-700"
            onClick={handleDelete}
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Product Images */}
      {product && user?.factoryId && (
        <div className="mb-6">
          <ProfessionalImageManager
            productId={product.id}
            factoryId={user.factoryId}
            maxImages={10}
            maxFileSize={5}
            showUpload={false}
            showMetadata={false}
          />
        </div>
      )}

      {/* Product Details Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="w-5 h-5 mr-2" />
                Product Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Name</label>
                <p className="text-lg font-semibold">{product.name}</p>
              </div>
              
              {product.shortDescription && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Short Description</label>
                  <p className="text-gray-900">{product.shortDescription}</p>
                </div>
              )}
              
              {product.description && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Description</label>
                  <p className="text-gray-900 whitespace-pre-wrap">{product.description}</p>
                </div>
              )}
              
              <div>
                <label className="text-sm font-medium text-gray-500">Category</label>
                <p className="text-gray-900">{product.category.name}</p>
              </div>
            </CardContent>
          </Card>

          {/* Pricing & Inventory */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <DollarSign className="w-5 h-5 mr-2" />
                Pricing & Inventory
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Base Price</label>
                <p className="text-xl font-bold text-green-600">
                  ${Number(product.basePrice).toFixed(2)} {product.currency}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Minimum Order Quantity</label>
                <p className="text-lg font-semibold">{product.minOrderQty}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Stock Quantity</label>
                <p className="text-lg font-semibold">{product.stockQuantity}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Stock Status</label>
                <Badge className={getStockStatusColor(product.stockStatus)}>
                  {product.stockStatus}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Product Status</label>
                <div className="mt-1">
                  <Badge className={getStatusColor(product.status)}>
                    {product.status}
                  </Badge>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Featured Product</label>
                <div className="mt-1">
                  <Badge className={product.isFeatured ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}>
                    {product.isFeatured ? 'Featured' : 'Not Featured'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Metadata
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Created</label>
                <p className="text-sm text-gray-900">
                  {new Date(product.createdAt).toLocaleDateString()}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Last Updated</label>
                <p className="text-sm text-gray-900">
                  {new Date(product.updatedAt).toLocaleDateString()}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Product ID</label>
                <p className="text-sm text-gray-600 font-mono">{product.id}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      </div>
    </ProtectedRoute>
  );
}
