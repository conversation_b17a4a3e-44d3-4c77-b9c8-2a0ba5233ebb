'use client';

import { useState, useRef, useEffect } from 'react';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../components/dashboard/dashboard-sidebar';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Badge } from '../../../components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../../../components/ui/avatar';
import { ScrollArea } from '../../../components/ui/scroll-area';
import { Separator } from '../../../components/ui/separator';
import { Textarea } from '../../../components/ui/textarea';
import {
  MessageSquare,
  Search,
  Plus,
  Filter,
  Send,
  Paperclip,
  Smile,
  MoreVertical,
  Phone,
  Video,
  Info,
  Archive,
  Star,
  Clock,
  Check,
  CheckCheck
} from 'lucide-react';
import { trpc } from '../../../lib/trpc';
import { LoadingSpinner } from '../../../components/ui/loading-spinner';
import { formatDateTime } from '../../../lib/utils';
import { NewConversationDialog } from '../../../components/dashboard/new-conversation-dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../../../components/ui/dropdown-menu';

export function MessagesClient() {
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewConversation, setShowNewConversation] = useState(false);
  const [filterType, setFilterType] = useState<'all' | 'unread' | 'archived'>('all');
  const [messageText, setMessageText] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch conversations
  const { data: conversationsData, isLoading, error } = trpc.messages.getConversations.useQuery({
    page: 1,
    limit: 100,
  });

  // Get unread count
  const { data: unreadCountData = 0 } = trpc.messages.getUnreadCount.useQuery();
  const unreadCount = typeof unreadCountData === 'number' ? unreadCountData :
                      (typeof unreadCountData === 'object' && unreadCountData && 'count' in unreadCountData) ?
                      (unreadCountData as { count: number }).count : 0;

  // Fetch messages for selected conversation
  const { data: messagesData, isLoading: messagesLoading } = trpc.messages.getMessages.useQuery(
    { conversationId: selectedConversationId!, page: 1, limit: 50 },
    { enabled: !!selectedConversationId }
  );

  // Send message mutation
  const sendMessageMutation = trpc.messages.sendMessage.useMutation({
    onSuccess: () => {
      setMessageText('');
      // Invalidate queries to refresh data
      trpc.useContext().messages.getMessages.invalidate();
      trpc.useContext().messages.getConversations.invalidate();
      trpc.useContext().messages.getUnreadCount.invalidate();
    },
  });

  const conversations = conversationsData?.data || [];
  const messages = messagesData?.data || [];
  const selectedConversation = conversations.find(c => c.id === selectedConversationId);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = () => {
    if (!messageText.trim() || !selectedConversationId) return;

    sendMessageMutation.mutate({
      conversationId: selectedConversationId,
      content: messageText.trim(),
      messageType: 'TEXT',
    });
  };

  // Filter conversations based on search and filter type
  const filteredConversations = conversations.filter(conv => {
    // Search filter
    if (searchQuery) {
      const participant = conv.participants.find(p => p.user.id !== conv.participants[0]?.user.id);
      const participantName = `${participant?.user.firstName} ${participant?.user.lastName}`.toLowerCase();
      const subject = conv.subject?.toLowerCase() || '';
      const lastMessageContent = conv.lastMessage?.content?.toLowerCase() || '';
      
      const matchesSearch = participantName.includes(searchQuery.toLowerCase()) || 
                           subject.includes(searchQuery.toLowerCase()) ||
                           lastMessageContent.includes(searchQuery.toLowerCase());
      
      if (!matchesSearch) return false;
    }

    // Type filter
    if (filterType === 'unread') {
      return conv.participants.some(p => p.unreadCount > 0);
    }
    
    // TODO: Add archived filter when implemented
    if (filterType === 'archived') {
      return false; // No archived conversations yet
    }

    return true;
  });

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4 animate-pulse" />
              <p className="text-gray-600">Loading messages...</p>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex items-center justify-center">
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-red-500">Failed to load messages</p>
                <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
                  Retry
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />

        <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Message Center</h1>
                <p className="text-gray-600">
                  Communicate with customers and team members
                </p>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 overflow-auto p-6">
            <div className="h-full flex">
              {/* Conversations Sidebar */}
              <div className="w-80 border-r border-gray-200 bg-white flex flex-col">
                {/* Sidebar Header */}
                <div className="p-4 border-b border-gray-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <MessageSquare className="w-5 h-5 text-blue-600" />
                      <h2 className="font-semibold text-gray-900">Messages</h2>
                      {unreadCount > 0 && (
                        <Badge variant="destructive" className="text-xs">
                          {unreadCount}
                        </Badge>
                      )}
                    </div>
                    <Button
                      size="sm"
                      onClick={() => setShowNewConversation(true)}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search conversations..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 h-9"
                    />
                  </div>
                {/* Conversations List */}
                <ScrollArea className="flex-1">
                  {filteredConversations.length === 0 ? (
                    <div className="text-center py-12 px-4">
                      <MessageSquare className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                      <h3 className="text-sm font-medium text-gray-900 mb-2">
                        {searchQuery ? 'No conversations found' : 'No messages yet'}
                      </h3>
                      <p className="text-xs text-gray-500 mb-4">
                        {searchQuery
                          ? 'Try adjusting your search terms'
                          : 'Start a conversation to connect with customers'
                        }
                      </p>
                      {!searchQuery && (
                        <Button size="sm" onClick={() => setShowNewConversation(true)}>
                          <Plus className="w-3 h-3 mr-2" />
                          Start Conversation
                        </Button>
                      )}
                    </div>
                  ) : (
                    <div className="divide-y divide-gray-100">
                      {filteredConversations.map((conversation) => {
                        const otherParticipant = conversation.participants.find(
                          p => p.user.id !== conversation.participants[0]?.user.id
                        );
                        const hasUnread = conversation.participants.some(p => p.unreadCount > 0);
                        const unreadCount = conversation.participants.find(p => p.unreadCount > 0)?.unreadCount || 0;
                        const isSelected = selectedConversationId === conversation.id;
                        const lastMessage = conversation.lastMessage;

                        return (
                          <div
                            key={conversation.id}
                            className={`p-4 cursor-pointer transition-all duration-200 hover:bg-gray-50 ${
                              isSelected ? 'bg-blue-50 border-r-2 border-r-blue-500' : ''
                            }`}
                            onClick={() => setSelectedConversationId(conversation.id)}
                          >
                            <div className="flex items-start space-x-3">
                              <div className="relative">
                                <Avatar className="w-10 h-10">
                                  <AvatarImage src={otherParticipant?.user.avatar} />
                                  <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white font-medium">
                                    {otherParticipant?.user.firstName?.[0]}{otherParticipant?.user.lastName?.[0]}
                                  </AvatarFallback>
                                </Avatar>
                                {hasUnread && (
                                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                                    <span className="text-xs text-white font-medium">{unreadCount}</span>
                                  </div>
                                )}
                              </div>

                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-1">
                                  <h4 className={`text-sm truncate ${hasUnread ? 'font-semibold text-gray-900' : 'font-medium text-gray-700'}`}>
                                    {otherParticipant?.user.firstName} {otherParticipant?.user.lastName}
                                  </h4>
                                  <span className="text-xs text-gray-500">
                                    {conversation.lastMessageAt && formatDateTime(conversation.lastMessageAt)}
                                  </span>
                                </div>

                                {lastMessage && (
                                  <p className={`text-sm truncate ${hasUnread ? 'font-medium text-gray-900' : 'text-gray-500'}`}>
                                    {lastMessage.content}
                                  </p>
                                )}

                                <div className="flex items-center justify-between mt-1">
                                  <Badge variant="outline" className="text-xs">
                                    {conversation.type.toLowerCase()}
                                  </Badge>
                                  <span className="text-xs text-gray-400">
                                    {conversation.messageCount} messages
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </ScrollArea>
              </div>

              {/* Message Content Area */}
              <div className="flex-1 flex flex-col bg-white">
                {selectedConversationId ? (
                  <>
                    {/* Chat Header */}
                    <div className="p-4 border-b border-gray-200 bg-white">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Avatar className="w-10 h-10">
                            <AvatarImage src={selectedConversation?.participants.find(p => p.user.id !== selectedConversation.participants[0]?.user.id)?.user.avatar} />
                            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white font-medium">
                              {selectedConversation?.participants.find(p => p.user.id !== selectedConversation.participants[0]?.user.id)?.user.firstName?.[0]}
                              {selectedConversation?.participants.find(p => p.user.id !== selectedConversation.participants[0]?.user.id)?.user.lastName?.[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <h3 className="font-semibold text-gray-900">
                              {selectedConversation?.participants.find(p => p.user.id !== selectedConversation.participants[0]?.user.id)?.user.firstName}{' '}
                              {selectedConversation?.participants.find(p => p.user.id !== selectedConversation.participants[0]?.user.id)?.user.lastName}
                            </h3>
                            <p className="text-sm text-gray-500">
                              {selectedConversation?.subject || 'No subject'}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm">
                            <Phone className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Video className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Info className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Messages Area */}
                    <ScrollArea className="flex-1 p-4">
                      {messagesLoading ? (
                        <div className="flex items-center justify-center h-32">
                          <LoadingSpinner />
                        </div>
                      ) : messages.length === 0 ? (
                        <div className="text-center py-12">
                          <MessageSquare className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                          <p className="text-gray-500">No messages yet. Start the conversation!</p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {messages.map((message) => {
                            const isOwnMessage = message.sender?.id === selectedConversation?.participants[0]?.user.id;

                            return (
                              <div
                                key={message.id}
                                className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
                              >
                                <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                                  isOwnMessage
                                    ? 'bg-blue-600 text-white'
                                    : 'bg-gray-100 text-gray-900'
                                }`}>
                                  <p className="text-sm">{message.content}</p>
                                  <div className={`flex items-center justify-between mt-1 text-xs ${
                                    isOwnMessage ? 'text-blue-100' : 'text-gray-500'
                                  }`}>
                                    <span>{formatDateTime(message.createdAt)}</span>
                                    {isOwnMessage && (
                                      <div className="ml-2">
                                        {message.readReceipts && message.readReceipts.length > 0 ? (
                                          <CheckCheck className="w-3 h-3" />
                                        ) : (
                                          <Check className="w-3 h-3" />
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                          <div ref={messagesEndRef} />
                        </div>
                      )}
                    </ScrollArea>

                    {/* Message Input */}
                    <div className="p-4 border-t border-gray-200 bg-white">
                      <div className="flex items-end space-x-2">
                        <Button variant="ghost" size="sm" className="mb-2">
                          <Paperclip className="w-4 h-4" />
                        </Button>
                        <div className="flex-1">
                          <Textarea
                            placeholder="Type your message..."
                            value={messageText}
                            onChange={(e) => setMessageText(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                handleSendMessage();
                              }
                            }}
                            className="min-h-[40px] max-h-32 resize-none"
                            rows={1}
                          />
                        </div>
                        <Button variant="ghost" size="sm" className="mb-2">
                          <Smile className="w-4 h-4" />
                        </Button>
                        <Button
                          onClick={handleSendMessage}
                          disabled={!messageText.trim() || sendMessageMutation.isLoading}
                          className="mb-2 bg-blue-600 hover:bg-blue-700"
                          size="sm"
                        >
                          <Send className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex-1 flex items-center justify-center bg-gray-50">
                    <div className="text-center">
                      <MessageSquare className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Select a conversation</h3>
                      <p className="text-gray-500 mb-4">Choose a conversation from the sidebar to start messaging</p>
                      <Button onClick={() => setShowNewConversation(true)} className="bg-blue-600 hover:bg-blue-700">
                        <Plus className="w-4 h-4 mr-2" />
                        Start New Conversation
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              {/* New Conversation Dialog */}
              <NewConversationDialog
                isOpen={showNewConversation}
                onClose={() => setShowNewConversation(false)}
                onConversationCreated={(conversationId) => {
                  setSelectedConversationId(conversationId);
                  setShowNewConversation(false);
                }}
              />
            </div>
          </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
