'use client';

import { DashboardLayout } from '../../components/dashboard/dashboard-layout';
import { ProtectedRoute } from '../../components/auth/protected-route';

// Force dynamic rendering to avoid prerendering issues with client components
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardLayout />
    </ProtectedRoute>
  );
}
