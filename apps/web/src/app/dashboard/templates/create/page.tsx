'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../../contexts/auth-context';
import { ProtectedRoute } from '../../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../../components/dashboard/dashboard-sidebar';
import { Button } from '../../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Input } from '../../../../components/ui/input';
import { Textarea } from '../../../../components/ui/textarea';
import { Label } from '../../../../components/ui/label';
import { Badge } from '../../../../components/ui/badge';
import {
  ArrowLeft,
  Save,
  Plus,
  Trash2,
  Package,
  Search
} from 'lucide-react';
import Link from 'next/link';
import { trpc } from '../../../../lib/trpc';
import { TemplateType } from '@fc-china/shared-types';

// Force dynamic rendering to avoid prerendering issues with client components
export const dynamic = 'force-dynamic';

interface TemplateItem {
  productId: string;
  variantId?: string;
  quantity: number;
  unitPrice?: number;
  specifications?: Record<string, any>;
  notes?: string;
  sortOrder: number;
  // For display purposes
  product?: {
    id: string;
    name: string;
    sku: string;
    basePrice: number;
    mainImage?: string;
  };
  variant?: {
    id: string;
    name: string;
    price: number;
  };
}

interface TemplateFormData {
  name: string;
  description: string;
  templateType: TemplateType;
  isPublic: boolean;
  defaultCustomerName: string;
  defaultCustomerEmail: string;
  defaultCustomerPhone: string;
  defaultCustomerCompany: string;
  defaultShippingStreet: string;
  defaultShippingCity: string;
  defaultShippingState: string;
  defaultShippingPostalCode: string;
  defaultShippingCountry: string;
  defaultCurrency: string;
  defaultNotes: string;
  items: TemplateItem[];
}

export default function CreateTemplatePage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showProductSearch, setShowProductSearch] = useState(false);
  const [productSearchQuery, setProductSearchQuery] = useState('');

  const [formData, setFormData] = useState<TemplateFormData>({
    name: '',
    description: '',
    templateType: 'STANDARD',
    isPublic: false,
    defaultCustomerName: '',
    defaultCustomerEmail: '',
    defaultCustomerPhone: '',
    defaultCustomerCompany: '',
    defaultShippingStreet: '',
    defaultShippingCity: '',
    defaultShippingState: '',
    defaultShippingPostalCode: '',
    defaultShippingCountry: '',
    defaultCurrency: 'USD',
    defaultNotes: '',
    items: [],
  });

  // Fetch products for selection
  const { data: productsData } = trpc.products.getAll.useQuery({
    page: 1,
    limit: 100,
    search: productSearchQuery || undefined,
  });

  const products = productsData?.data || [];

  const createTemplate = trpc.orderTemplates.create.useMutation({
    onSuccess: (data) => {
      router.push(`/dashboard/templates/${data.id}`);
    },
    onError: (error) => {
      console.error('Failed to create template:', error);
      alert('Failed to create template. Please try again.');
    },
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.items.length === 0) {
      alert('Please add at least one item to the template.');
      return;
    }

    setIsSubmitting(true);
    try {
      await createTemplate.mutateAsync({
        name: formData.name,
        description: formData.description || undefined,
        templateType: formData.templateType,
        isPublic: formData.isPublic,
        defaultCustomerName: formData.defaultCustomerName || undefined,
        defaultCustomerEmail: formData.defaultCustomerEmail || undefined,
        defaultCustomerPhone: formData.defaultCustomerPhone || undefined,
        defaultCustomerCompany: formData.defaultCustomerCompany || undefined,
        defaultShippingStreet: formData.defaultShippingStreet || undefined,
        defaultShippingCity: formData.defaultShippingCity || undefined,
        defaultShippingState: formData.defaultShippingState || undefined,
        defaultShippingPostalCode: formData.defaultShippingPostalCode || undefined,
        defaultShippingCountry: formData.defaultShippingCountry || undefined,
        defaultCurrency: formData.defaultCurrency as any,
        defaultNotes: formData.defaultNotes || undefined,
        items: formData.items.map(item => ({
          productId: item.productId,
          variantId: item.variantId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          specifications: item.specifications,
          notes: item.notes,
          sortOrder: item.sortOrder,
        })),
      });
    } catch (error) {
      console.error('Error creating template:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const addProduct = (product: any) => {
    const newItem: TemplateItem = {
      productId: product.id,
      quantity: 1,
      unitPrice: product.basePrice,
      sortOrder: formData.items.length,
      product: {
        id: product.id,
        name: product.name,
        sku: product.sku,
        basePrice: product.basePrice,
        mainImage: product.images?.find((img: any) => img.isMain)?.url,
      },
    };

    setFormData(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
    setShowProductSearch(false);
    setProductSearchQuery('');
  };

  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  const updateItem = (index: number, updates: Partial<TemplateItem>) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index ? { ...item, ...updates } : item
      ),
    }));
  };

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/dashboard/templates">
                  <Button variant="outline" size="sm">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back
                  </Button>
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Create Template</h1>
                  <p className="text-gray-600">Create a new order template</p>
                </div>
              </div>
              
              <Button 
                onClick={handleSubmit}
                disabled={isSubmitting || !formData.name || formData.items.length === 0}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Save className="w-4 h-4 mr-2" />
                {isSubmitting ? 'Creating...' : 'Create Template'}
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Template Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter template name"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="templateType">Template Type</Label>
                      <select
                        id="templateType"
                        value={formData.templateType}
                        onChange={(e) => setFormData(prev => ({ ...prev, templateType: e.target.value as TemplateType }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="STANDARD">Standard</option>
                        <option value="RECURRING">Recurring</option>
                        <option value="CUSTOMER">Customer</option>
                        <option value="SEASONAL">Seasonal</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Enter template description"
                      rows={3}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="isPublic"
                      checked={formData.isPublic}
                      onChange={(e) => setFormData(prev => ({ ...prev, isPublic: e.target.checked }))}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="isPublic">Make this template public</Label>
                  </div>
                </CardContent>
              </Card>

              {/* Template Items */}
              <TemplateItemsSection 
                items={formData.items}
                onAddProduct={() => setShowProductSearch(true)}
                onRemoveItem={removeItem}
                onUpdateItem={updateItem}
              />

              {/* Product Search Modal */}
              {showProductSearch && (
                <ProductSearchModal
                  products={products}
                  searchQuery={productSearchQuery}
                  onSearchChange={setProductSearchQuery}
                  onSelectProduct={addProduct}
                  onClose={() => setShowProductSearch(false)}
                />
              )}
            </form>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

function TemplateItemsSection({ items, onAddProduct, onRemoveItem, onUpdateItem }: any) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Package className="w-5 h-5 mr-2" />
            Template Items ({items.length})
          </CardTitle>
          <Button type="button" onClick={onAddProduct} variant="outline">
            <Plus className="w-4 h-4 mr-2" />
            Add Product
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {items.length === 0 ? (
          <div className="text-center py-8">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">No items added yet</p>
            <Button type="button" onClick={onAddProduct}>
              <Plus className="w-4 h-4 mr-2" />
              Add Your First Product
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {items.map((item: TemplateItem, index: number) => (
              <div key={index} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  {item.product?.mainImage ? (
                    <img
                      src={item.product.mainImage}
                      alt={item.product.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <Package className="w-6 h-6 text-gray-400" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900">{item.product?.name}</h4>
                  <p className="text-sm text-gray-600">SKU: {item.product?.sku}</p>
                  <p className="text-sm text-gray-600">
                    Base Price: ${item.product?.basePrice.toFixed(2)}
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  <div>
                    <Label className="text-xs">Quantity</Label>
                    <Input
                      type="number"
                      min="1"
                      value={item.quantity}
                      onChange={(e) => onUpdateItem(index, { quantity: parseInt(e.target.value) || 1 })}
                      className="w-20"
                    />
                  </div>
                  <div>
                    <Label className="text-xs">Unit Price</Label>
                    <Input
                      type="number"
                      min="0"
                      step="0.01"
                      value={item.unitPrice || ''}
                      onChange={(e) => onUpdateItem(index, { unitPrice: parseFloat(e.target.value) || undefined })}
                      placeholder={item.product?.basePrice.toFixed(2)}
                      className="w-24"
                    />
                  </div>
                  <div className="text-right">
                    <Label className="text-xs">Total</Label>
                    <p className="text-sm font-medium">
                      ${((item.unitPrice || item.product?.basePrice || 0) * item.quantity).toFixed(2)}
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => onRemoveItem(index)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function ProductSearchModal({ products, searchQuery, onSearchChange, onSelectProduct, onClose }: any) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Add Product</h3>
          <Button variant="outline" size="sm" onClick={onClose}>
            ×
          </Button>
        </div>

        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search products..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="max-h-96 overflow-y-auto space-y-2">
          {products.length === 0 ? (
            <div className="text-center py-8">
              <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No products found</p>
            </div>
          ) : (
            products.map((product: any) => (
              <div
                key={product.id}
                className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                onClick={() => onSelectProduct(product)}
              >
                <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  {product.images?.find((img: any) => img.isMain) ? (
                    <img
                      src={product.images.find((img: any) => img.isMain).url}
                      alt={product.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <Package className="w-5 h-5 text-gray-400" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 truncate">{product.name}</h4>
                  <p className="text-sm text-gray-600">SKU: {product.sku}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    ${product.basePrice.toFixed(2)}
                  </p>
                  <Badge className={
                    product.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }>
                    {product.status}
                  </Badge>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
