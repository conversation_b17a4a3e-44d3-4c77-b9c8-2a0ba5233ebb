'use client';

import { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '../../../../contexts/auth-context';
import { ProtectedRoute } from '../../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../../components/dashboard/dashboard-sidebar';
import { <PERSON><PERSON> } from '../../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Badge } from '../../../../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../../components/ui/tabs';
import {
  ArrowLeft,
  Edit,
  Copy,
  Trash2,
  Package,
  ShoppingCart,
  Calendar,
  DollarSign,
  User,
  MapPin,
  FileText,
  Plus,
  Eye
} from 'lucide-react';
import <PERSON> from 'next/link';
import { trpc } from '../../../../lib/trpc';
import { TemplateType } from '@fc-china/shared-types';

// Force dynamic rendering to avoid prerendering issues with client components
export const dynamic = 'force-dynamic';

const templateTypeColors = {
  STANDARD: 'bg-blue-100 text-blue-800',
  RECURRING: 'bg-green-100 text-green-800',
  CUSTOMER: 'bg-purple-100 text-purple-800',
  SEASONAL: 'bg-orange-100 text-orange-800',
};

const templateTypeLabels = {
  STANDARD: 'Standard',
  RECURRING: 'Recurring',
  CUSTOMER: 'Customer',
  SEASONAL: 'Seasonal',
};

export default function TemplateDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const templateId = params.id as string;

  // Fetch template details
  const { data: template, isLoading, refetch } = trpc.orderTemplates.getById.useQuery({
    id: templateId,
  });

  const deleteTemplate = trpc.orderTemplates.delete.useMutation({
    onSuccess: () => {
      router.push('/dashboard/templates');
    },
  });

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete "${template?.name}"?`)) {
      try {
        await deleteTemplate.mutateAsync({ id: templateId });
      } catch (error) {
        console.error('Failed to delete template:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
            <div className="flex-1 overflow-auto p-6">
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-64 bg-gray-200 rounded-lg"></div>
              </div>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (!template) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
            <div className="flex-1 overflow-auto p-6">
              <Card>
                <CardContent className="p-12 text-center">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Template not found
                  </h3>
                  <p className="text-gray-600 mb-6">
                    The template you're looking for doesn't exist or has been deleted.
                  </p>
                  <Link href="/dashboard/templates">
                    <Button>
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Back to Templates
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  const totalItems = template.items.length;
  const totalOrders = template.orders?.length || 0;
  const activeRecurringOrders = template.recurringOrders?.filter(ro => ro.isActive).length || 0;

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/dashboard/templates">
                  <Button variant="outline" size="sm">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back
                  </Button>
                </Link>
                <div>
                  <div className="flex items-center space-x-3 mb-1">
                    <h1 className="text-2xl font-bold text-gray-900">{template.name}</h1>
                    <Badge className={templateTypeColors[template.templateType]}>
                      {templateTypeLabels[template.templateType]}
                    </Badge>
                    <Badge className={template.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                      {template.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  {template.description && (
                    <p className="text-gray-600">{template.description}</p>
                  )}
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Link href={`/dashboard/templates/${template.id}/create-order`}>
                  <Button className="bg-green-600 hover:bg-green-700">
                    <Plus className="w-4 h-4 mr-2" />
                    Create Order
                  </Button>
                </Link>
                <Link href={`/dashboard/templates/${template.id}/edit`}>
                  <Button variant="outline">
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </Button>
                </Link>
                <Button 
                  variant="outline" 
                  onClick={handleDelete}
                  disabled={deleteTemplate.isPending}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <Package className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{totalItems}</div>
                  <div className="text-sm text-gray-600">Template Items</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <ShoppingCart className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{totalOrders}</div>
                  <div className="text-sm text-gray-600">Orders Created</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <Calendar className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{activeRecurringOrders}</div>
                  <div className="text-sm text-gray-600">Active Recurring</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <DollarSign className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-gray-900">
                    {template.estimatedTotalAmount ? `$${template.estimatedTotalAmount.toFixed(2)}` : 'N/A'}
                  </div>
                  <div className="text-sm text-gray-600">Estimated Total</div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            <Tabs defaultValue="items" className="space-y-6">
              <TabsList>
                <TabsTrigger value="items">Template Items</TabsTrigger>
                <TabsTrigger value="defaults">Default Settings</TabsTrigger>
                <TabsTrigger value="orders">Generated Orders</TabsTrigger>
                <TabsTrigger value="recurring">Recurring Orders</TabsTrigger>
              </TabsList>

              <TabsContent value="items">
                <TemplateItemsTab template={template} />
              </TabsContent>

              <TabsContent value="defaults">
                <DefaultSettingsTab template={template} />
              </TabsContent>

              <TabsContent value="orders">
                <GeneratedOrdersTab template={template} />
              </TabsContent>

              <TabsContent value="recurring">
                <RecurringOrdersTab template={template} />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}

function TemplateItemsTab({ template }: { template: any }) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Package className="w-5 h-5 mr-2" />
          Template Items ({template.items.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {template.items.length === 0 ? (
          <div className="text-center py-8">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No items in this template</p>
          </div>
        ) : (
          <div className="space-y-4">
            {template.items.map((item: any, index: number) => (
              <div key={item.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  {item.product.mainImage ? (
                    <img
                      src={item.product.mainImage}
                      alt={item.product.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <Package className="w-6 h-6 text-gray-400" />
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900">{item.product.name}</h4>
                  <p className="text-sm text-gray-600">SKU: {item.product.sku}</p>
                  {item.variant && (
                    <p className="text-sm text-gray-600">Variant: {item.variant.name}</p>
                  )}
                  {item.specifications && Object.keys(item.specifications).length > 0 && (
                    <div className="mt-1">
                      <p className="text-xs text-gray-500">Specifications:</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {Object.entries(item.specifications).map(([key, value]) => (
                          <Badge key={key} variant="outline" className="text-xs">
                            {key}: {String(value)}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="text-right flex-shrink-0">
                  <div className="text-lg font-semibold text-gray-900">
                    Qty: {item.quantity}
                  </div>
                  <div className="text-sm text-gray-600">
                    ${item.unitPrice ? item.unitPrice.toFixed(2) : item.product.basePrice.toFixed(2)}
                  </div>
                  <div className="text-sm font-medium text-gray-900">
                    Total: ${((item.unitPrice || item.product.basePrice) * item.quantity).toFixed(2)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function DefaultSettingsTab({ template }: { template: any }) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Customer Defaults */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="w-5 h-5 mr-2" />
            Default Customer Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-700">Customer Name</label>
            <p className="text-gray-900">{template.defaultCustomerName || 'Not set'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Email</label>
            <p className="text-gray-900">{template.defaultCustomerEmail || 'Not set'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Phone</label>
            <p className="text-gray-900">{template.defaultCustomerPhone || 'Not set'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Company</label>
            <p className="text-gray-900">{template.defaultCustomerCompany || 'Not set'}</p>
          </div>
        </CardContent>
      </Card>

      {/* Shipping Defaults */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MapPin className="w-5 h-5 mr-2" />
            Default Shipping Address
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-700">Street</label>
            <p className="text-gray-900">{template.defaultShippingStreet || 'Not set'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">City</label>
            <p className="text-gray-900">{template.defaultShippingCity || 'Not set'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">State</label>
            <p className="text-gray-900">{template.defaultShippingState || 'Not set'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Postal Code</label>
            <p className="text-gray-900">{template.defaultShippingPostalCode || 'Not set'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Country</label>
            <p className="text-gray-900">{template.defaultShippingCountry || 'Not set'}</p>
          </div>
        </CardContent>
      </Card>

      {/* Template Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Template Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-700">Default Currency</label>
            <p className="text-gray-900">{template.defaultCurrency}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Public Template</label>
            <p className="text-gray-900">{template.isPublic ? 'Yes' : 'No'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Default Notes</label>
            <p className="text-gray-900">{template.defaultNotes || 'Not set'}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function GeneratedOrdersTab({ template }: { template: any }) {
  const orders = template.orders || [];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <ShoppingCart className="w-5 h-5 mr-2" />
          Generated Orders ({orders.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {orders.length === 0 ? (
          <div className="text-center py-8">
            <ShoppingCart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No orders created from this template yet</p>
            <Link href={`/dashboard/templates/${template.id}/create-order`} className="mt-4 inline-block">
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create First Order
              </Button>
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {orders.map((order: any) => (
              <div key={order.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">{order.orderNumber}</h4>
                  <p className="text-sm text-gray-600">
                    Created: {new Date(order.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-right">
                  <Badge className={
                    order.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                    order.status === 'CONFIRMED' ? 'bg-blue-100 text-blue-800' :
                    order.status === 'IN_PRODUCTION' ? 'bg-purple-100 text-purple-800' :
                    order.status === 'SHIPPED' ? 'bg-green-100 text-green-800' :
                    order.status === 'DELIVERED' ? 'bg-green-100 text-green-800' :
                    'bg-red-100 text-red-800'
                  }>
                    {order.status}
                  </Badge>
                  <p className="text-sm font-medium text-gray-900 mt-1">
                    ${order.totalAmount.toFixed(2)}
                  </p>
                </div>
                <Link href={`/dashboard/orders/${order.id}`}>
                  <Button variant="outline" size="sm">
                    <Eye className="w-4 h-4 mr-1" />
                    View
                  </Button>
                </Link>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function RecurringOrdersTab({ template }: { template: any }) {
  const recurringOrders = template.recurringOrders || [];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Calendar className="w-5 h-5 mr-2" />
          Recurring Orders ({recurringOrders.length})
        </CardTitle>
      </CardHeader>
      <CardContent>
        {recurringOrders.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No recurring orders set up for this template</p>
            <Link href={`/dashboard/templates/${template.id}/create-recurring`} className="mt-4 inline-block">
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create Recurring Order
              </Button>
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {recurringOrders.map((recurring: any) => (
              <div key={recurring.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">{recurring.name}</h4>
                  <p className="text-sm text-gray-600">
                    Frequency: {recurring.frequency}
                  </p>
                  <p className="text-sm text-gray-600">
                    Next Run: {new Date(recurring.nextRunDate).toLocaleDateString()}
                  </p>
                </div>
                <div className="text-right">
                  <Badge className={recurring.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                    {recurring.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
                <Link href={`/dashboard/recurring-orders/${recurring.id}`}>
                  <Button variant="outline" size="sm">
                    <Eye className="w-4 h-4 mr-1" />
                    View
                  </Button>
                </Link>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
