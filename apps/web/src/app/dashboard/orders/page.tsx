'use client';

import { useState, useEffect } from 'react';

// Force dynamic rendering to avoid prerendering issues with client components
export const dynamic = 'force-dynamic';
import { useAuth } from '../../../contexts/auth-context';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../components/dashboard/dashboard-sidebar';
import { Button } from '../../../components/ui/button';
import { Plus } from 'lucide-react';
import Link from 'next/link';
import { BulkOperationsToolbar } from '../../../components/orders/bulk-operations-toolbar';
import { OrderListWithSelection } from '../../../components/orders/order-list-with-selection';
import { toast } from '../../../hooks/use-toast';

interface Order {
  id: string;
  orderNumber: string;
  status: string;
  customerName: string;
  customerEmail: string;
  customerCompany?: string;
  totalAmount: number;
  currency: string;
  itemCount: number;
  orderDate: string;
  requiredDate?: string;
  shippedDate?: string;
  deliveredDate?: string;
  assignedTo?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  factory: {
    id: string;
    name: string;
  };
}

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
}

export default function EnhancedOrdersPage() {
  const { user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Selection state
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  
  // Filter and search state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Load orders
  const loadOrders = async () => {
    if (!user?.factoryId) return;

    setLoading(true);
    try {
      const params = new URLSearchParams({
        factoryId: user.factoryId,
        page: currentPage.toString(),
        limit: '20',
        ...(statusFilter && { status: statusFilter }),
        ...(searchTerm && { search: searchTerm }),
      });

      const response = await fetch(`/api/orders?${params}`);
      if (response.ok) {
        const data = await response.json();
        setOrders(data.data || []);
        setTotalPages(data.pagination?.totalPages || 1);
        console.log('✅ Orders loaded:', data.data?.length || 0);
      } else {
        console.error('❌ Failed to load orders');
        setError('Failed to load orders. Please refresh the page.');
      }
    } catch (error) {
      console.error('❌ Error loading orders:', error);
      setError('Failed to load orders. Please refresh the page.');
    } finally {
      setLoading(false);
    }
  };

  // Load team members
  const loadTeamMembers = async () => {
    if (!user?.factoryId) return;

    try {
      // This would use tRPC in a real implementation
      // For now, we'll create a mock API endpoint
      const response = await fetch(`/api/team-members?factoryId=${user.factoryId}`);
      if (response.ok) {
        const data = await response.json();
        setTeamMembers(data.data || []);
      }
    } catch (error) {
      console.error('❌ Error loading team members:', error);
      // Set some mock data for now
      setTeamMembers([
        { id: '1', name: 'John Doe', email: '<EMAIL>', role: 'Manager' },
        { id: '2', name: 'Jane Smith', email: '<EMAIL>', role: 'Coordinator' },
      ]);
    }
  };

  useEffect(() => {
    loadOrders();
  }, [user?.factoryId, currentPage, statusFilter, searchTerm]);

  useEffect(() => {
    loadTeamMembers();
  }, [user?.factoryId]);

  // Bulk operations handlers
  const handleBulkUpdateStatus = async (orderIds: string[], status: string, notes?: string, trackingNumber?: string) => {
    try {
      const response = await fetch('/api/orders/bulk-update-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderIds,
          status,
          notes,
          trackingNumber,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update orders');
      }

      // Reload orders and clear selection
      await loadOrders();
      setSelectedOrders([]);
      
      toast({
        title: 'Success',
        description: `Successfully updated ${orderIds.length} orders to ${status}`,
      });
    } catch (error) {
      console.error('❌ Error updating orders:', error);
      throw error;
    }
  };

  const handleBulkAssign = async (orderIds: string[], assignedToId: string) => {
    try {
      const response = await fetch('/api/orders/bulk-assign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderIds,
          assignedToId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to assign orders');
      }

      // Reload orders and clear selection
      await loadOrders();
      setSelectedOrders([]);
      
      const assignee = teamMembers.find(member => member.id === assignedToId);
      toast({
        title: 'Success',
        description: `Successfully assigned ${orderIds.length} orders to ${assignee?.name}`,
      });
    } catch (error) {
      console.error('❌ Error assigning orders:', error);
      throw error;
    }
  };

  const handleBulkExport = async (orderIds: string[], format: 'CSV' | 'EXCEL') => {
    try {
      const response = await fetch('/api/orders/bulk-export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderIds,
          format,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to export orders');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `orders_export_${new Date().toISOString().split('T')[0]}.${format.toLowerCase()}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: 'Success',
        description: `Successfully exported ${orderIds.length} orders`,
      });
    } catch (error) {
      console.error('❌ Error exporting orders:', error);
      throw error;
    }
  };

  const handleClearSelection = () => {
    setSelectedOrders([]);
  };

  const handleSearchChange = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleStatusFilterChange = (status: string) => {
    setStatusFilter(status);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  if (error) {
    return (
      <ProtectedRoute>
        <div className="flex h-screen bg-gray-50">
          <DashboardSidebar />
          <main className="flex-1 overflow-y-auto">
            <div className="p-8">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Orders</h2>
                <p className="text-gray-600 mb-4">{error}</p>
                <Button onClick={() => window.location.reload()}>
                  Refresh Page
                </Button>
              </div>
            </div>
          </main>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        <main className="flex-1 overflow-y-auto">
          <div className="p-8">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Orders</h1>
                <p className="text-gray-600 mt-2">
                  Manage and track all your factory orders
                </p>
              </div>
              <Link href="/dashboard/orders/create">
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Order
                </Button>
              </Link>
            </div>

            {/* Bulk Operations Toolbar */}
            <BulkOperationsToolbar
              selectedOrders={selectedOrders}
              onClearSelection={handleClearSelection}
              onBulkUpdate={handleBulkUpdateStatus}
              onBulkAssign={handleBulkAssign}
              onBulkExport={handleBulkExport}
              teamMembers={teamMembers}
              loading={loading}
            />

            {/* Orders List with Selection */}
            <OrderListWithSelection
              orders={orders}
              loading={loading}
              selectedOrders={selectedOrders}
              onSelectionChange={setSelectedOrders}
              searchTerm={searchTerm}
              onSearchChange={handleSearchChange}
              statusFilter={statusFilter}
              onStatusFilterChange={handleStatusFilterChange}
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </main>
      </div>
    </ProtectedRoute>
  );
}
