'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '../../../../contexts/auth-context';
import { ProtectedRoute } from '../../../../components/auth/protected-route';
import { DashboardSidebar } from '../../../../components/dashboard/dashboard-sidebar';
import { Button } from '../../../../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../../../../components/ui/card';
import { Input } from '../../../../components/ui/input';
import { Label } from '../../../../components/ui/label';
import { Textarea } from '../../../../components/ui/textarea';
import { 
  ArrowLeft,
  Save,
  Plus,
  Trash2,
  Package,
  User,
  MapPin,
  Calendar,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

// Force dynamic rendering to avoid prerendering issues with client components
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

interface Product {
  id: string;
  name: string;
  basePrice: number;
  currency: string;
  minOrderQty: number;
  maxOrderQty?: number;
}

interface OrderItem {
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export default function CreateOrderPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [productsLoading, setProductsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    customerCompany: '',
    shippingStreet: '',
    shippingCity: '',
    shippingState: '',
    shippingPostalCode: '',
    shippingCountry: '',
    requiredDate: '',
    notes: '',
  });

  const [orderItems, setOrderItems] = useState<OrderItem[]>([]);

  // Load products
  useEffect(() => {
    const loadProducts = async () => {
      if (!user?.factoryId) return;

      setProductsLoading(true);
      try {
        const response = await fetch(`/api/products?factoryId=${user.factoryId}&status=ACTIVE&limit=100`);
        if (response.ok) {
          const data = await response.json();
          setProducts(data.data || []);
          console.log('✅ Products loaded:', data.data?.length || 0);
        } else {
          console.error('❌ Failed to load products');
          setError('Failed to load products. Please refresh the page.');
        }
      } catch (error) {
        console.error('❌ Error loading products:', error);
        setError('Failed to load products. Please refresh the page.');
      } finally {
        setProductsLoading(false);
      }
    };

    loadProducts();
  }, [user?.factoryId]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (error) setError(null);
  };

  const addOrderItem = () => {
    setOrderItems(prev => [...prev, {
      productId: '',
      productName: '',
      quantity: 1,
      unitPrice: 0,
      totalPrice: 0,
    }]);
  };

  const removeOrderItem = (index: number) => {
    setOrderItems(prev => prev.filter((_, i) => i !== index));
  };

  const updateOrderItem = (index: number, field: keyof OrderItem, value: string | number) => {
    setOrderItems(prev => prev.map((item, i) => {
      if (i !== index) return item;

      const updatedItem = { ...item, [field]: value };

      // If product changed, update price and name
      if (field === 'productId') {
        const product = products.find(p => p.id === value);
        if (product) {
          updatedItem.productName = product.name;
          updatedItem.unitPrice = product.basePrice;
          updatedItem.totalPrice = updatedItem.quantity * product.basePrice;
        }
      }

      // If quantity or unit price changed, recalculate total
      if (field === 'quantity' || field === 'unitPrice') {
        updatedItem.totalPrice = updatedItem.quantity * updatedItem.unitPrice;
      }

      return updatedItem;
    }));
  };

  const calculateTotal = () => {
    return orderItems.reduce((sum, item) => sum + item.totalPrice, 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user?.factoryId) {
      setError('No factory ID found. Please ensure you are logged in.');
      return;
    }

    // Validate required fields
    if (!formData.customerName || !formData.customerEmail || orderItems.length === 0) {
      setError('Please fill in customer name, email, and add at least one order item.');
      return;
    }

    // Validate order items
    for (const item of orderItems) {
      if (!item.productId || item.quantity <= 0 || item.unitPrice <= 0) {
        setError('Please ensure all order items have valid products, quantities, and prices.');
        return;
      }
    }

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('🔄 Creating order:', {
        customerName: formData.customerName,
        factoryId: user.factoryId,
        itemsCount: orderItems.length
      });

      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          factoryId: user.factoryId,
          items: orderItems.map(item => ({
            productId: item.productId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
          })),
          shippingAddress: {
            street: formData.shippingStreet,
            city: formData.shippingCity,
            state: formData.shippingState,
            postalCode: formData.shippingPostalCode,
            country: formData.shippingCountry,
          },
          requiredDate: formData.requiredDate ? new Date(formData.requiredDate) : undefined,
        }),
      });

      if (response.ok) {
        const order = await response.json();
        console.log('✅ Order created successfully:', order.orderNumber);
        setSuccess('Order created successfully! Redirecting...');

        // Redirect after a short delay to show success message
        setTimeout(() => {
          router.push(`/dashboard/orders/${order.id}`);
        }, 1500);
      } else {
        const errorData = await response.json();
        console.error('❌ Order creation failed:', response.status, errorData);
        setError(`Failed to create order: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('❌ Order creation error:', error);
      setError('Failed to create order. Please check your connection and try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50">
        <DashboardSidebar />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button variant="ghost" onClick={() => router.back()}>
                  <ArrowLeft className="w-4 h-4" />
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Create New Order</h1>
                  <p className="text-gray-600 mt-1">Create a new order for a customer</p>
                </div>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-auto p-6">
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2 text-red-700">
                <AlertCircle className="w-5 h-5" />
                {error}
              </div>
            )}

            {success && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-2 text-green-700">
                <CheckCircle className="w-5 h-5" />
                {success}
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Customer Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="w-5 h-5" />
                    Customer Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="customerName">Customer Name *</Label>
                      <Input
                        id="customerName"
                        name="customerName"
                        value={formData.customerName}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="customerEmail">Email *</Label>
                      <Input
                        id="customerEmail"
                        name="customerEmail"
                        type="email"
                        value={formData.customerEmail}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="customerPhone">Phone</Label>
                      <Input
                        id="customerPhone"
                        name="customerPhone"
                        value={formData.customerPhone}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div>
                      <Label htmlFor="customerCompany">Company</Label>
                      <Input
                        id="customerCompany"
                        name="customerCompany"
                        value={formData.customerCompany}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Shipping Address */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-5 h-5" />
                    Shipping Address
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="shippingStreet">Street Address</Label>
                    <Input
                      id="shippingStreet"
                      name="shippingStreet"
                      value={formData.shippingStreet}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="shippingCity">City</Label>
                      <Input
                        id="shippingCity"
                        name="shippingCity"
                        value={formData.shippingCity}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div>
                      <Label htmlFor="shippingState">State/Province</Label>
                      <Input
                        id="shippingState"
                        name="shippingState"
                        value={formData.shippingState}
                        onChange={handleInputChange}
                      />
                    </div>
                    <div>
                      <Label htmlFor="shippingPostalCode">Postal Code</Label>
                      <Input
                        id="shippingPostalCode"
                        name="shippingPostalCode"
                        value={formData.shippingPostalCode}
                        onChange={handleInputChange}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="shippingCountry">Country</Label>
                    <Input
                      id="shippingCountry"
                      name="shippingCountry"
                      value={formData.shippingCountry}
                      onChange={handleInputChange}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Order Items */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Package className="w-5 h-5" />
                      Order Items
                    </div>
                    <Button type="button" onClick={addOrderItem} className="flex items-center gap-2">
                      <Plus className="w-4 h-4" />
                      Add Item
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {orderItems.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Package className="mx-auto h-12 w-12 text-gray-400" />
                      <p className="mt-2">No items added yet</p>
                      <p className="text-sm">Click "Add Item" to start building the order</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {orderItems.map((item, index) => (
                        <div key={index} className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg">
                          <div className="flex-1 grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                              <Label>Product</Label>
                              <select
                                value={item.productId}
                                onChange={(e) => updateOrderItem(index, 'productId', e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                aria-label={`Product for item ${index + 1}`}
                                required
                              >
                                <option value="">Select Product</option>
                                {products.map((product) => (
                                  <option key={product.id} value={product.id}>
                                    {product.name}
                                  </option>
                                ))}
                              </select>
                            </div>
                            <div>
                              <Label>Quantity</Label>
                              <Input
                                type="number"
                                min="1"
                                value={item.quantity}
                                onChange={(e) => updateOrderItem(index, 'quantity', parseInt(e.target.value) || 1)}
                                required
                              />
                            </div>
                            <div>
                              <Label>Unit Price</Label>
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                value={item.unitPrice}
                                onChange={(e) => updateOrderItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                                required
                              />
                            </div>
                            <div>
                              <Label>Total</Label>
                              <Input
                                type="number"
                                value={item.totalPrice.toFixed(2)}
                                readOnly
                                className="bg-gray-50"
                              />
                            </div>
                          </div>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeOrderItem(index)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                      
                      {/* Order Total */}
                      <div className="pt-4 border-t border-gray-200">
                        <div className="flex justify-between items-center">
                          <span className="text-lg font-semibold">Total Amount:</span>
                          <span className="text-xl font-bold">${calculateTotal().toFixed(2)}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Additional Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Additional Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="requiredDate">Required Date</Label>
                    <Input
                      id="requiredDate"
                      name="requiredDate"
                      type="date"
                      value={formData.requiredDate}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      rows={3}
                      placeholder="Any additional notes or special instructions..."
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Submit Button */}
              <div className="flex justify-end gap-4">
                <Button type="button" variant="outline" onClick={() => router.back()}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading || orderItems.length === 0} className="flex items-center gap-2">
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Save className="w-4 h-4" />
                  )}
                  {loading ? 'Creating...' : 'Create Order'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
