{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@auth0/nextjs-auth0": "^4.8.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.10.0", "@payloadcms/db-mongodb": "^3.48.0", "@payloadcms/next": "^3.48.0", "@payloadcms/plugin-cloud-storage": "^3.48.0", "@payloadcms/richtext-slate": "^3.48.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^4.36.1", "@tanstack/react-query-devtools": "^4.36.1", "@trpc/client": "^10.45.0", "@trpc/react-query": "^10.45.0", "@trpc/server": "^10.45.0", "@types/passport": "^1.0.17", "@types/passport-auth0": "^1.0.9", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^3.0.6", "lucide-react": "^0.303.0", "next": "15.4.2", "passport": "^0.7.0", "passport-auth0": "^1.4.4", "payload": "^3.48.0", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.61.1", "recharts": "^2.15.4", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "node-loader": "^2.1.0", "tailwindcss": "^4", "typescript": "^5"}}