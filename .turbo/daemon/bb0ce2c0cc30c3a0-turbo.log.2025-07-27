2025-07-27T10:50:13.313733Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2417")}
2025-07-27T10:50:13.313746Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T10:51:13.613557Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2418")}
2025-07-27T10:51:13.613576Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T10:51:13.613612Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T10:52:10.613342Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/payload-types.ts")}
2025-07-27T10:52:10.613351Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T10:52:13.813631Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2419")}
2025-07-27T10:52:13.813641Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T10:52:13.814163Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T10:53:14.012670Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2420")}
2025-07-27T10:53:14.012679Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T10:53:14.043942Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T10:53:14.113137Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2420")}
2025-07-27T10:53:14.113145Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T10:53:14.113161Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T10:53:50.313353Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/tsconfig.json")}
2025-07-27T10:53:50.313368Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T10:54:14.313668Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2421")}
2025-07-27T10:54:14.313689Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T10:54:14.313708Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T10:54:22.513335Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".payload-backup")}
2025-07-27T10:54:22.513395Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T10:54:36.913465Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".payload-backup/(payload)"), AnchoredSystemPathBuf("apps/web/src/app/(payload)")}
2025-07-27T10:54:36.913477Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T10:54:43.313402Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".payload-backup/payload"), AnchoredSystemPathBuf("apps/web/src/payload")}
2025-07-27T10:54:43.313415Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T10:54:49.513120Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/utils/payload-integration.ts"), AnchoredSystemPathBuf(".payload-backup/payload-integration.ts")}
2025-07-27T10:54:49.513133Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }, WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T10:54:56.813267Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".payload-backup/payload-auth"), AnchoredSystemPathBuf("apps/web/src/app/api/auth/payload-auth")}
2025-07-27T10:54:56.813283Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T10:55:14.511974Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2422")}
2025-07-27T10:55:14.511982Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T10:55:14.545529Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T10:55:14.613645Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2422")}
2025-07-27T10:55:14.613653Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T10:55:14.613667Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T10:55:22.113397Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".payload-backup/api-payload"), AnchoredSystemPathBuf("apps/web/src/app/api/payload")}
2025-07-27T10:55:22.113407Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T10:55:37.413534Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/tsconfig.json")}
2025-07-27T10:55:37.413547Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T10:56:14.813054Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2423")}
2025-07-27T10:56:14.813090Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T10:56:14.813156Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T10:57:15.012362Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2424")}
2025-07-27T10:57:15.012371Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T10:57:15.024779Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T10:57:15.213481Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/package.json")}
2025-07-27T10:57:15.213491Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T10:58:15.311791Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2425")}
2025-07-27T10:58:15.311800Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T10:58:15.311818Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T10:58:31.413227Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared-types/package.json")}
2025-07-27T10:58:31.413238Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@fc-china/shared-types"), path: AnchoredSystemPathBuf("packages/shared-types") }}))
2025-07-27T10:59:15.512591Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2426")}
2025-07-27T10:59:15.512599Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T10:59:15.512615Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T10:59:21.515489Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".gitignore")}
2025-07-27T10:59:21.515500Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T11:00:15.712049Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2427")}
2025-07-27T11:00:15.712058Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:00:15.749892Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:00:15.813086Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2427")}
2025-07-27T11:00:15.813094Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:00:15.813134Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:01:16.012453Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2428")}
2025-07-27T11:01:16.012467Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:01:16.057936Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:02:16.311430Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2429")}
2025-07-27T11:02:16.311439Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:02:16.311456Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:03:16.511177Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2430")}
2025-07-27T11:03:16.511187Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:03:16.511207Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:03:48.511788Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/package.json")}
2025-07-27T11:03:48.511796Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:03:59.412626Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/shared-types/package.json")}
2025-07-27T11:03:59.412636Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@fc-china/shared-types"), path: AnchoredSystemPathBuf("packages/shared-types") }}))
2025-07-27T11:04:16.711105Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2431")}
2025-07-27T11:04:16.711114Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:04:16.757512Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:04:16.811721Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2431")}
2025-07-27T11:04:16.811730Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:04:16.811751Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:04:24.711771Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)")}
2025-07-27T11:04:24.711779Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:04:35.811551Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload")}
2025-07-27T11:04:35.811564Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:04:43.511655Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/utils/payload-integration.ts")}
2025-07-27T11:04:43.511665Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:04:54.012262Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/auth/payload-auth")}
2025-07-27T11:04:54.012272Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:05:04.211065Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/payload")}
2025-07-27T11:05:04.211102Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:05:17.011444Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2432")}
2025-07-27T11:05:17.011455Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:05:17.011478Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:05:17.811601Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf(".payload-backup")}
2025-07-27T11:05:17.811651Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T11:06:17.211515Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2433")}
2025-07-27T11:06:17.211535Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:06:17.211946Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:06:37.425079Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/collections/Factories.ts")}
2025-07-27T11:06:37.425090Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:06:59.036886Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/globals/FactorySettings.ts")}
2025-07-27T11:06:59.036900Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:07:17.438914Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2434")}
2025-07-27T11:07:17.438924Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:07:17.480151Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:07:17.539641Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2434")}
2025-07-27T11:07:17.539653Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:07:17.539698Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:08:17.640602Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2435")}
2025-07-27T11:08:17.640635Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:08:17.679721Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:08:17.740906Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2435")}
2025-07-27T11:08:17.740924Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:08:17.740945Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:09:17.841135Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2436")}
2025-07-27T11:09:17.841171Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:09:17.880772Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:09:17.940881Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2436")}
2025-07-27T11:09:17.940890Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:09:17.940928Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:10:18.040311Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2437")}
2025-07-27T11:10:18.040365Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:10:18.082722Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:11:18.341171Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2438")}
2025-07-27T11:11:18.341180Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:11:18.341197Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:12:18.540401Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2439")}
2025-07-27T11:12:18.540421Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:12:18.540438Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:12:50.441213Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)/admin/[[...segments]]/page.tsx")}
2025-07-27T11:12:50.441225Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:13:16.140641Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)/layout.tsx")}
2025-07-27T11:13:16.140650Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:13:18.741255Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2440")}
2025-07-27T11:13:18.741270Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:13:18.765289Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:14:19.042410Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2441")}
2025-07-27T11:14:19.042421Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:14:19.042440Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:14:32.741961Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)/admin/importMap.js")}
2025-07-27T11:14:32.741968Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:14:41.142526Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)/layout.tsx")}
2025-07-27T11:14:41.142537Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:15:19.241771Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2442")}
2025-07-27T11:15:19.241796Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:15:19.266975Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:15:23.342115Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/hooks/auth0-sync.ts")}
2025-07-27T11:15:23.342125Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:15:35.542382Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/hooks/auth0-sync.ts")}
2025-07-27T11:15:35.542392Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:15:49.741505Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/auth/auth0-strategy.ts")}
2025-07-27T11:15:49.741513Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:16:19.541664Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2443")}
2025-07-27T11:16:19.541684Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:16:19.541730Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:16:42.942801Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/payload/[...slug]/route.ts")}
2025-07-27T11:16:42.942819Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:17:19.742628Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2444")}
2025-07-27T11:17:19.742640Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:17:19.815050Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:17:19.842167Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2444")}
2025-07-27T11:17:19.842174Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:17:19.842213Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:18:20.042728Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2445")}
2025-07-27T11:18:20.042747Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:18:20.042780Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:19:20.242762Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2446")}
2025-07-27T11:19:20.242786Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:19:20.277505Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:19:52.941892Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/payload/[...slug]/route.ts")}
2025-07-27T11:19:52.941900Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:20:20.542973Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2447")}
2025-07-27T11:20:20.543020Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:20:20.543095Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:21:02.642822Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)/admin/[[...segments]]/page.tsx")}
2025-07-27T11:21:02.642835Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:21:20.741995Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2448")}
2025-07-27T11:21:20.742008Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:21:20.761862Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:21:21.742722Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)/layout.tsx")}
2025-07-27T11:21:21.742731Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:21:42.142845Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)/layout.tsx")}
2025-07-27T11:21:42.142860Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:22:08.843485Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/prisma/schema.prisma")}
2025-07-27T11:22:08.843494Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:22:21.043405Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2449")}
2025-07-27T11:22:21.043414Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:22:21.043430Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:22:51.947619Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/prisma/schema.prisma")}
2025-07-27T11:22:51.947631Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:23:21.250618Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2450")}
2025-07-27T11:23:21.250629Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:23:21.256064Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:24:13.051107Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)/admin/[[...segments]]/page.tsx")}
2025-07-27T11:24:13.051125Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:24:21.452012Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2451")}
2025-07-27T11:24:21.452035Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:24:21.497014Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:24:21.551175Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2451")}
2025-07-27T11:24:21.551181Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:24:21.551196Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:24:49.051386Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)/layout.tsx")}
2025-07-27T11:24:49.051396Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:25:08.751013Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)/layout.tsx")}
2025-07-27T11:25:08.751038Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:25:21.751600Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2452")}
2025-07-27T11:25:21.751620Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:25:21.751647Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:25:38.451666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/auth/payload-auth/route.ts")}
2025-07-27T11:25:38.451674Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:26:04.953636Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/auth/token/route.ts")}
2025-07-27T11:26:04.953645Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:26:22.052105Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2453")}
2025-07-27T11:26:22.052118Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:26:22.052134Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:27:22.251195Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2454")}
2025-07-27T11:27:22.251204Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:27:22.258601Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:28:01.752194Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/route.ts")}
2025-07-27T11:28:01.752203Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:28:22.452808Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2455")}
2025-07-27T11:28:22.452829Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:28:22.510512Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:28:22.552638Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2455")}
2025-07-27T11:28:22.552651Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:28:22.552665Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:29:20.253112Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)/admin/[[...segments]]/page.tsx")}
2025-07-27T11:29:20.253135Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:29:22.752747Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2456")}
2025-07-27T11:29:22.752756Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:29:22.756920Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:29:38.853341Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/auth/token/route.ts")}
2025-07-27T11:29:38.853351Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:30:05.852940Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/route.ts")}
2025-07-27T11:30:05.852950Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:30:23.053154Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2457")}
2025-07-27T11:30:23.053163Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:30:23.053181Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:30:39.753108Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T11:30:39.753118Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:31:11.853761Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/analytics/page.tsx")}
2025-07-27T11:31:11.853771Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:31:23.253026Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2458")}
2025-07-27T11:31:23.253037Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:31:23.253057Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:32:23.553845Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2459")}
2025-07-27T11:32:23.553864Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:32:23.553910Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:33:23.753993Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2460")}
2025-07-27T11:33:23.754003Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:33:23.754029Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:33:49.853825Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/(payload)/admin/[[...segments]]/page.tsx")}
2025-07-27T11:33:49.853834Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:34:03.554329Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T11:34:03.554339Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:34:23.954337Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2461")}
2025-07-27T11:34:23.954361Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:34:23.994518Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:34:24.054639Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2461")}
2025-07-27T11:34:24.054646Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:34:24.054661Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:34:46.454719Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T11:34:46.454729Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:35:02.854808Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T11:35:02.854820Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:35:14.053913Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T11:35:14.053924Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:35:24.253961Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2462")}
2025-07-27T11:35:24.253970Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:35:24.253990Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:35:31.555069Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T11:35:31.555094Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:35:51.654581Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T11:35:51.654589Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:36:07.454313Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T11:36:07.454328Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:36:24.454546Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2463")}
2025-07-27T11:36:24.454555Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:36:24.493134Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:36:24.555296Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2463")}
2025-07-27T11:36:24.555303Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:36:24.555341Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:36:28.556830Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/forms/product-form-validation.tsx")}
2025-07-27T11:36:28.556849Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:36:42.155039Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/ui/collapsible.tsx")}
2025-07-27T11:36:42.155050Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:37:11.455000Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/utils/payload-integration.ts")}
2025-07-27T11:37:11.455021Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:37:24.755001Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2464")}
2025-07-27T11:37:24.755010Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:37:24.755026Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:38:24.954723Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2465")}
2025-07-27T11:38:24.954740Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:38:24.980898Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:39:25.255957Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2466")}
2025-07-27T11:39:25.255977Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:39:25.256022Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:40:25.455346Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2467")}
2025-07-27T11:40:25.455355Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:40:25.487331Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:40:32.056045Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("package.json")}
2025-07-27T11:40:32.056054Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(All)
2025-07-27T11:40:32.356062Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("package-lock.json")}
2025-07-27T11:40:32.356072Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T11:41:25.755700Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2468")}
2025-07-27T11:41:25.755708Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:41:25.755729Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:42:25.956211Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2469")}
2025-07-27T11:42:25.956222Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:42:25.956236Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:43:26.156658Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2470")}
2025-07-27T11:43:26.156667Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:43:26.198866Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:44:26.457662Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2471")}
2025-07-27T11:44:26.457690Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:44:26.457754Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:44:28.056439Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/payload-types.ts")}
2025-07-27T11:44:28.056453Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:44:57.857102Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T11:44:57.857117Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:45:15.756733Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T11:45:15.756741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:45:26.656816Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2472")}
2025-07-27T11:45:26.656825Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:45:26.672028Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:46:23.757789Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T11:46:23.757800Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:46:26.958317Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2473")}
2025-07-27T11:46:26.958327Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:46:26.958394Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:46:39.759245Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T11:46:39.759261Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:47:27.159990Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2474")}
2025-07-27T11:47:27.159998Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:47:27.167788Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:47:40.560981Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T11:47:40.561004Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:48:06.361076Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T11:48:06.361088Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:48:27.460763Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2475")}
2025-07-27T11:48:27.460773Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:48:27.460794Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:48:29.362023Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/products/bulk-import/route.ts")}
2025-07-27T11:48:29.362032Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:49:22.461956Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/products/bulk-import/route.ts")}
2025-07-27T11:49:22.461965Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:49:27.661284Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2476")}
2025-07-27T11:49:27.661293Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:49:27.661331Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:50:03.362200Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/messages/page.tsx")}
2025-07-27T11:50:03.362209Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:50:23.861171Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/messages/page.tsx")}
2025-07-27T11:50:23.861180Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:50:27.861826Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2477")}
2025-07-27T11:50:27.861845Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:50:27.915798Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:50:27.961975Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2477")}
2025-07-27T11:50:27.961994Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:50:27.962032Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:51:28.162452Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2478")}
2025-07-27T11:51:28.162464Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:51:28.162484Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:52:04.062637Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/messages/page.tsx")}
2025-07-27T11:52:04.062646Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:52:22.662963Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/products/page.tsx")}
2025-07-27T11:52:22.662979Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:52:28.462944Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2479")}
2025-07-27T11:52:28.462953Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:52:28.496252Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:52:28.564540Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2479")}
2025-07-27T11:52:28.564551Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:52:28.564570Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:53:07.564745Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T11:53:07.564756Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:53:28.963471Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2480")}
2025-07-27T11:53:28.963484Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:53:28.963505Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:53:33.062673Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T11:53:33.062682Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:54:14.263482Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T11:54:14.263494Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:54:29.263167Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2481")}
2025-07-27T11:54:29.263187Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:54:29.263235Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:55:29.464000Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2482")}
2025-07-27T11:55:29.464044Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:55:29.464070Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:56:29.663509Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2483")}
2025-07-27T11:56:29.663520Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:56:29.724581Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:56:29.762731Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2483")}
2025-07-27T11:56:29.762741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:56:29.762787Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:56:51.764196Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/recurring-orders/page.tsx")}
2025-07-27T11:56:51.764219Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:57:06.164245Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T11:57:06.164300Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:57:22.563340Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/page.tsx")}
2025-07-27T11:57:22.563373Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:57:29.964099Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2484")}
2025-07-27T11:57:29.964108Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:57:29.964123Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:57:39.463676Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/create/page.tsx")}
2025-07-27T11:57:39.463694Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:57:57.164890Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/page.tsx")}
2025-07-27T11:57:57.164909Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:58:08.564836Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/page.tsx")}
2025-07-27T11:58:08.564846Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:58:30.164026Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2485")}
2025-07-27T11:58:30.164036Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:58:30.198410Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:58:56.164882Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/page.tsx")}
2025-07-27T11:58:56.164892Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:59:24.965249Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T11:59:24.965262Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T11:59:30.465265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2486")}
2025-07-27T11:59:30.465273Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T11:59:30.465317Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T11:59:47.964282Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T11:59:47.964306Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:00:25.965563Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T12:00:25.965583Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:00:30.664832Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2487")}
2025-07-27T12:00:30.664847Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:00:30.665961Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:00:46.564077Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T12:00:46.564089Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:01:17.065901Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/utils/payload-integration.ts")}
2025-07-27T12:01:17.065942Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:01:30.864630Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2488")}
2025-07-27T12:01:30.864644Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:01:30.900103Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:02:31.065782Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2489")}
2025-07-27T12:02:31.065794Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:02:31.093208Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:03:31.266167Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2490")}
2025-07-27T12:03:31.266177Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:03:31.300353Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:04:31.466641Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2491")}
2025-07-27T12:04:31.466674Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:04:31.512710Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:04:31.566968Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2491")}
2025-07-27T12:04:31.566981Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:04:31.567026Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:04:53.866178Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/utils/payload-integration.ts")}
2025-07-27T12:04:53.866188Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:05:26.666768Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/dashboard-overview.tsx")}
2025-07-27T12:05:26.666787Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:05:31.766913Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2492")}
2025-07-27T12:05:31.766950Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:05:31.795528Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:06:13.567098Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/dashboard-sidebar.tsx")}
2025-07-27T12:06:13.567107Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:06:32.067118Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2493")}
2025-07-27T12:06:32.067127Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:06:32.067144Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:06:37.667398Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/contexts/auth-context.tsx")}
2025-07-27T12:06:37.667422Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:07:18.167886Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/onboarding/factory-onboarding-wizard.tsx")}
2025-07-27T12:07:18.167896Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:07:32.267555Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2494")}
2025-07-27T12:07:32.267564Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:07:32.284781Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:08:02.168293Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/onboarding/factory-onboarding-wizard.tsx")}
2025-07-27T12:08:02.168302Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:08:32.568706Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2495")}
2025-07-27T12:08:32.568715Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:08:32.568741Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:08:54.968744Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/onboarding/factory-onboarding-wizard.tsx")}
2025-07-27T12:08:54.968753Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:09:32.768780Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2496")}
2025-07-27T12:09:32.768792Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:09:32.778426Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:10:33.069064Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2497")}
2025-07-27T12:10:33.069074Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:10:33.069090Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:11:21.569420Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/new-conversation-dialog.tsx")}
2025-07-27T12:11:21.569429Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:11:33.270237Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2498")}
2025-07-27T12:11:33.270249Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:11:33.281717Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:12:01.274849Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/message-attachment.tsx")}
2025-07-27T12:12:01.274925Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:12:25.474376Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/message-attachment.tsx")}
2025-07-27T12:12:25.474385Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:12:33.473673Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2499")}
2025-07-27T12:12:33.473688Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:12:33.473714Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:12:50.374726Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/message-attachment.tsx")}
2025-07-27T12:12:50.374735Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:13:33.675160Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2500")}
2025-07-27T12:13:33.675197Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:13:33.689105Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:14:33.874734Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2501")}
2025-07-27T12:14:33.874753Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:14:33.896480Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:15:34.076210Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2502")}
2025-07-27T12:15:34.076219Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:15:34.098446Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:16:34.275756Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2503")}
2025-07-27T12:16:34.275845Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:16:34.316089Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:16:34.376472Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2503")}
2025-07-27T12:16:34.376485Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:16:34.376595Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:17:34.576810Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2504")}
2025-07-27T12:17:34.576819Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:17:34.576846Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:18:34.776301Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2505")}
2025-07-27T12:18:34.776325Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:18:34.776364Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:19:08.477338Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/utils/payload-integration.ts")}
2025-07-27T12:19:08.477347Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:19:34.976135Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2506")}
2025-07-27T12:19:34.976144Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:19:35.007024Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:20:35.277813Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2507")}
2025-07-27T12:20:35.277822Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:20:35.277839Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:20:55.376990Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/utils/payload-integration.ts")}
2025-07-27T12:20:55.376999Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:21:35.477531Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2508")}
2025-07-27T12:21:35.477547Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:21:35.483022Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:22:27.778245Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/utils/payload-integration.ts")}
2025-07-27T12:22:27.778257Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:22:35.678163Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2509")}
2025-07-27T12:22:35.678190Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:22:35.712975Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:22:35.778108Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2509")}
2025-07-27T12:22:35.778116Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:22:35.778127Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:23:35.978769Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2510")}
2025-07-27T12:23:35.978782Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:23:35.978800Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:24:36.178004Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2511")}
2025-07-27T12:24:36.178029Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:24:36.212416Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:24:36.279545Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2511")}
2025-07-27T12:24:36.279553Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:24:36.279568Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:24:50.178546Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/page.tsx")}
2025-07-27T12:24:50.178561Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:25:32.279733Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T12:25:32.279743Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:25:36.479899Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2512")}
2025-07-27T12:25:36.479923Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:25:36.479981Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:25:48.078910Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T12:25:48.078920Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:26:36.679135Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2513")}
2025-07-27T12:26:36.679144Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:26:36.679245Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:27:36.979732Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2514")}
2025-07-27T12:27:36.979741Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:27:36.979756Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:28:37.280884Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2515")}
2025-07-27T12:28:37.280902Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:28:37.280959Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:29:37.480436Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2516")}
2025-07-27T12:29:37.480446Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:29:37.492667Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:30:12.681515Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-location-manager.tsx")}
2025-07-27T12:30:12.681535Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:30:37.781132Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2517")}
2025-07-27T12:30:37.781141Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:30:37.781164Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:31:37.981686Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2518")}
2025-07-27T12:31:37.981699Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:31:37.981734Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:31:43.082021Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-location-manager.tsx")}
2025-07-27T12:31:43.082041Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:32:38.182999Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2519")}
2025-07-27T12:32:38.183013Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:32:38.217675Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:32:38.282810Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2519")}
2025-07-27T12:32:38.282834Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:32:38.282934Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:33:38.483042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2520")}
2025-07-27T12:33:38.483053Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:33:38.493251Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:34:38.856992Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2521")}
2025-07-27T12:34:38.857003Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:34:38.857023Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:35:39.254485Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2522")}
2025-07-27T12:35:39.254502Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:35:39.282485Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:36:39.753061Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2523")}
2025-07-27T12:36:39.753071Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:36:39.760038Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:37:40.052429Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2524")}
2025-07-27T12:37:40.052443Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:37:40.052485Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:38:10.052036Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-location-manager.tsx")}
2025-07-27T12:38:10.052045Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:38:40.252018Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2525")}
2025-07-27T12:38:40.252061Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:38:40.254705Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:39:43.547476Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2526")}
2025-07-27T12:39:43.547489Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:39:43.614273Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:39:43.646862Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2526")}
2025-07-27T12:39:43.646872Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:39:43.646899Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:40:55.742146Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2527")}
2025-07-27T12:40:55.742155Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:40:55.754411Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:48:28.601771Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2528")}
2025-07-27T12:48:28.601804Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:48:28.640897Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:48:28.703005Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2528")}
2025-07-27T12:48:28.703014Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:48:28.703034Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:50:09.298331Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2529")}
2025-07-27T12:50:09.298347Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:50:09.898167Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:51:09.483076Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2530")}
2025-07-27T12:51:09.483096Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:51:09.483117Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:51:26.683171Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-location-manager.tsx")}
2025-07-27T12:51:26.683190Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:52:09.682340Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2531")}
2025-07-27T12:52:09.682360Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:52:09.689228Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:52:43.982861Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-location-manager.tsx")}
2025-07-27T12:52:43.982871Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:53:09.881895Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2532")}
2025-07-27T12:53:09.881921Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:53:09.926747Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:53:09.982938Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2532")}
2025-07-27T12:53:09.982947Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:53:09.982965Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:54:07.283178Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-location-manager.tsx")}
2025-07-27T12:54:07.283196Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:54:10.182690Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2533")}
2025-07-27T12:54:10.182705Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:54:10.183273Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:55:10.481645Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2534")}
2025-07-27T12:55:10.481707Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:55:10.481760Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:55:18.583357Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-location-manager.tsx")}
2025-07-27T12:55:18.583386Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:56:10.682195Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2535")}
2025-07-27T12:56:10.682206Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:56:10.778749Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:57:10.982305Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2536")}
2025-07-27T12:57:10.982314Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:57:10.982417Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:57:52.481723Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-location-manager.tsx")}
2025-07-27T12:57:52.481737Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:58:11.182719Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2537")}
2025-07-27T12:58:11.182739Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:58:11.217113Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:58:11.281296Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2537")}
2025-07-27T12:58:11.281306Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:58:11.281330Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T12:58:41.382439Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-location-manager.tsx")}
2025-07-27T12:58:41.382449Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:59:02.981838Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-location-manager.tsx")}
2025-07-27T12:59:02.981848Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T12:59:11.481843Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2538")}
2025-07-27T12:59:11.481853Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T12:59:11.481879Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:00:11.682139Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2539")}
2025-07-27T13:00:11.682151Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:00:11.704246Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:00:13.582259Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-location-manager.tsx")}
2025-07-27T13:00:13.582277Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:01:11.981690Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2540")}
2025-07-27T13:01:11.981701Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:01:11.981720Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:02:12.181601Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2541")}
2025-07-27T13:02:12.181629Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:02:12.190806Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:02:17.981439Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-location-manager.tsx")}
2025-07-27T13:02:17.981448Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:03:12.481634Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2542")}
2025-07-27T13:03:12.481643Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:03:12.481661Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:04:12.681576Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2543")}
2025-07-27T13:04:12.681585Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:04:12.681600Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:05:12.781441Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2544")}
2025-07-27T13:05:12.781453Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:05:12.837125Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:05:12.881345Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2544")}
2025-07-27T13:05:12.881352Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:05:12.881368Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:05:42.781109Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/dashboard-overview.tsx")}
2025-07-27T13:05:42.781118Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:06:13.080945Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2545")}
2025-07-27T13:06:13.080955Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:06:13.092638Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:06:20.581459Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/dashboard-sidebar.tsx")}
2025-07-27T13:06:20.581469Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:07:13.381293Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2546")}
2025-07-27T13:07:13.381303Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:07:13.381318Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:07:25.081777Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/message-attachment.tsx")}
2025-07-27T13:07:25.081788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:08:13.581768Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2547")}
2025-07-27T13:08:13.581793Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:08:13.591495Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:08:50.880886Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/new-conversation-dialog.tsx")}
2025-07-27T13:08:50.880898Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:08:50.981359Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/new-conversation-dialog.tsx")}
2025-07-27T13:08:50.981372Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:08:50.981418Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:09:13.881135Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2548")}
2025-07-27T13:09:13.881148Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:09:13.881178Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:10:14.080713Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2549")}
2025-07-27T13:10:14.080734Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:10:14.122086Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:10:14.181235Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2549")}
2025-07-27T13:10:14.181243Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:10:14.181260Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:10:37.680944Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/forms/product-form-validation.tsx")}
2025-07-27T13:10:37.680963Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:11:14.381174Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2550")}
2025-07-27T13:11:14.381227Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:11:14.383271Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:12:14.580872Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2551")}
2025-07-27T13:12:14.580881Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:12:14.607513Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:12:17.080886Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/stock-movement-manager.tsx")}
2025-07-27T13:12:17.080922Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:13:14.780241Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/inventory-reservation-manager.tsx")}
2025-07-27T13:13:14.780257Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:13:14.880905Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2552")}
2025-07-27T13:13:14.880915Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:13:14.880940Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:14:15.080708Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2553")}
2025-07-27T13:14:15.080718Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:14:15.104071Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:15:15.380476Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2554")}
2025-07-27T13:15:15.380484Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:15:15.380499Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:16:15.579575Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2555")}
2025-07-27T13:16:15.579586Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:16:15.600731Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:17:15.880354Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2556")}
2025-07-27T13:17:15.880363Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:17:15.880394Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:17:57.680563Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/stock-movement-manager.tsx")}
2025-07-27T13:17:57.680576Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:18:16.078987Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2557")}
2025-07-27T13:18:16.079000Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:18:16.095348Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:18:44.079595Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/message-attachment.tsx")}
2025-07-27T13:18:44.079625Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:19:16.380197Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2558")}
2025-07-27T13:19:16.380210Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:19:16.380233Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:19:17.679210Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/new-conversation-dialog.tsx")}
2025-07-27T13:19:17.679219Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:20:16.579865Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2559")}
2025-07-27T13:20:16.579874Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:20:16.606347Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:20:30.869269Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/dashboard/new-conversation-dialog.tsx")}
2025-07-27T13:20:30.869289Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:21:16.850922Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2560")}
2025-07-27T13:21:16.850949Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:21:16.851014Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:22:17.049740Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2561")}
2025-07-27T13:22:17.049749Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:22:17.049787Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:23:17.350014Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2562")}
2025-07-27T13:23:17.350026Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:23:17.350042Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:23:18.749921Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/stock-movement-manager.tsx")}
2025-07-27T13:23:18.749932Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:23:18.850281Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/stock-movement-manager.tsx")}
2025-07-27T13:23:18.850291Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:23:18.850348Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:24:17.548656Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2563")}
2025-07-27T13:24:17.548682Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:24:17.561846Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:25:17.747754Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2564")}
2025-07-27T13:25:17.747764Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:25:17.805785Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:25:17.848104Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2564")}
2025-07-27T13:25:17.848113Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:25:17.848147Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:26:18.048042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2565")}
2025-07-27T13:26:18.048050Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:26:18.056147Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:27:18.246854Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2566")}
2025-07-27T13:27:18.246863Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:27:18.246885Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:28:18.447750Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2567")}
2025-07-27T13:28:18.447785Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:28:18.499169Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:28:18.546924Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2567")}
2025-07-27T13:28:18.546935Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:28:18.546952Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:29:18.746185Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2568")}
2025-07-27T13:29:18.746211Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:29:18.746253Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:30:18.945791Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2569")}
2025-07-27T13:30:18.945806Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:30:18.946327Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:31:19.145568Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2570")}
2025-07-27T13:31:19.145580Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:31:19.145611Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:32:19.345128Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2571")}
2025-07-27T13:32:19.345137Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:32:19.351972Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:32:40.349088Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/recurring-orders/page.tsx")}
2025-07-27T13:32:40.349099Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:33:19.644773Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2572")}
2025-07-27T13:33:19.644795Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:33:19.644856Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:33:35.244532Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/advanced-image-upload.tsx")}
2025-07-27T13:33:35.244541Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:34:19.843009Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2573")}
2025-07-27T13:34:19.843018Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:34:19.845263Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:34:28.944159Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/enhanced-image-manager.tsx")}
2025-07-27T13:34:28.944175Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:35:20.143164Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2574")}
2025-07-27T13:35:20.143193Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:35:20.143214Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:35:39.542549Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/professional-image-manager.tsx")}
2025-07-27T13:35:39.542559Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:36:20.342486Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2575")}
2025-07-27T13:36:20.342494Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:36:20.342510Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:36:49.043117Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/onboarding/factory-onboarding-wizard.tsx")}
2025-07-27T13:36:49.043136Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:37:06.742448Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/contexts/auth-context.tsx")}
2025-07-27T13:37:06.742459Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:37:20.541467Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2576")}
2025-07-27T13:37:20.541481Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:37:20.589323Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:37:20.642436Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2576")}
2025-07-27T13:37:20.642443Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:37:20.642469Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:38:20.841609Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2577")}
2025-07-27T13:38:20.841617Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:38:20.841632Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:38:59.341801Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/recurring-orders/page.tsx")}
2025-07-27T13:38:59.341812Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:39:21.040605Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2578")}
2025-07-27T13:39:21.042322Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:39:21.077638Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:40:13.440233Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/enhanced-image-manager.tsx")}
2025-07-27T13:40:13.440247Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:40:21.341309Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2579")}
2025-07-27T13:40:21.341318Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:40:21.341336Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:41:21.639822Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2580")}
2025-07-27T13:41:21.639837Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:41:21.639860Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:41:35.440245Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/professional-image-manager.tsx")}
2025-07-27T13:41:35.440256Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:42:10.639637Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/professional-image-manager.tsx")}
2025-07-27T13:42:10.639656Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:42:21.840035Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2581")}
2025-07-27T13:42:21.840046Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:42:21.848976Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:43:22.139970Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2582")}
2025-07-27T13:43:22.139979Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:43:22.139995Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:43:24.339489Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/auth/payload-admin-button.tsx")}
2025-07-27T13:43:24.339499Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:44:00.339166Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/auth/payload-admin-button.tsx")}
2025-07-27T13:44:00.339175Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:44:22.438317Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2583")}
2025-07-27T13:44:22.438326Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:44:22.451938Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:45:00.538936Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/auth/payload-admin-button.tsx")}
2025-07-27T13:45:00.538945Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:45:22.639030Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2584")}
2025-07-27T13:45:22.639050Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:45:22.674352Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:45:22.737868Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2584")}
2025-07-27T13:45:22.737891Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:45:22.737915Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:45:53.338493Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/auth/payload-admin-button.tsx")}
2025-07-27T13:45:53.338502Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:46:23.039474Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2585")}
2025-07-27T13:46:23.039505Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:46:23.039526Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:46:48.237191Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/auth/payload-admin-button.tsx")}
2025-07-27T13:46:48.237199Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:47:23.437194Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2586")}
2025-07-27T13:47:23.437211Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:47:23.471883Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:47:23.538023Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2586")}
2025-07-27T13:47:23.538030Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:47:23.538043Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:48:00.436258Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/recurring-orders/page.tsx")}
2025-07-27T13:48:00.436277Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:48:23.736788Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2587")}
2025-07-27T13:48:23.736798Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:48:23.736827Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:48:51.536584Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/auth/payload-admin-button.tsx")}
2025-07-27T13:48:51.536593Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:49:18.336561Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/enhanced-image-manager.tsx")}
2025-07-27T13:49:18.336571Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:49:23.935791Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2588")}
2025-07-27T13:49:23.935800Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:49:23.949866Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:50:24.235722Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2589")}
2025-07-27T13:50:24.235732Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:50:24.235754Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:50:50.335782Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/components/product/professional-image-manager.tsx")}
2025-07-27T13:50:50.335791Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:51:24.436655Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2590")}
2025-07-27T13:51:24.436711Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:51:24.471188Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:52:24.746842Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2591")}
2025-07-27T13:52:24.746851Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:52:24.746885Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:53:13.859043Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/recurring-orders/page.tsx")}
2025-07-27T13:53:13.859090Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:53:24.957818Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2592")}
2025-07-27T13:53:24.957827Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:53:24.981249Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:53:27.358486Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/test/integration/page.tsx")}
2025-07-27T13:53:27.358509Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:53:39.558731Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/test/product-edit-integration/page.tsx")}
2025-07-27T13:53:39.558744Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:53:51.557809Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/test/product-edit-integration/page.tsx")}
2025-07-27T13:53:51.557820Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:54:04.857855Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/test/product-edit-integration/page.tsx")}
2025-07-27T13:54:04.857866Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:54:17.057389Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/test/product-edit-integration/page.tsx")}
2025-07-27T13:54:17.057399Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:54:25.159045Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2593")}
2025-07-27T13:54:25.159054Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:54:25.183201Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:54:31.658388Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/test/product-edit-integration/page.tsx")}
2025-07-27T13:54:31.658399Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:55:25.458196Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2594")}
2025-07-27T13:55:25.458215Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:55:25.458243Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:55:25.558940Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/hooks/use-api.ts")}
2025-07-27T13:55:25.558950Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:55:50.158263Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/hooks/use-payload-auth.ts")}
2025-07-27T13:55:50.158276Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:56:25.658211Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2595")}
2025-07-27T13:56:25.658227Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:56:25.658243Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:56:35.258123Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/recurring-orders/page.tsx")}
2025-07-27T13:56:35.258144Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T13:57:25.858752Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2596")}
2025-07-27T13:57:25.858762Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:57:25.893804Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:57:25.957941Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2596")}
2025-07-27T13:57:25.957949Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:57:25.958060Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:58:26.057436Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2597")}
2025-07-27T13:58:26.057462Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:58:26.093975Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:58:26.158112Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2597")}
2025-07-27T13:58:26.158124Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:58:26.158162Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T13:59:26.357627Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2598")}
2025-07-27T13:59:26.357643Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T13:59:26.363464Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:00:26.657860Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2599")}
2025-07-27T14:00:26.657870Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:00:26.657919Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:04:35.862615Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2600")}
2025-07-27T14:04:35.862636Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:04:35.867697Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:05:49.647087Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2601")}
2025-07-27T14:05:49.647123Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:05:49.649817Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:06:49.846892Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2602")}
2025-07-27T14:06:49.846910Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:06:49.846932Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:07:50.046666Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2603")}
2025-07-27T14:07:50.046719Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:07:50.085739Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:08:27.146405Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/recurring-orders/page.tsx")}
2025-07-27T14:08:27.146429Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:08:50.346952Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2604")}
2025-07-27T14:08:50.346962Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:08:50.346982Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:08:59.246193Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/recurring-orders/page.tsx")}
2025-07-27T14:08:59.246203Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:09:50.545401Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2605")}
2025-07-27T14:09:50.545412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:09:50.560698Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:10:50.545789Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:10:50.545801Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:10:50.845937Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2606")}
2025-07-27T14:10:50.845945Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:10:50.845962Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:11:00.544929Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:11:00.544940Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:11:21.945464Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:11:21.945524Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:11:51.046042Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2607")}
2025-07-27T14:11:51.046052Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:11:51.048397Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:11:55.346006Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:11:55.346027Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:12:09.845706Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:12:09.845732Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:12:51.345816Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2608")}
2025-07-27T14:12:51.345827Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:12:51.345843Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:13:48.644876Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:13:48.644961Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:13:51.544476Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2609")}
2025-07-27T14:13:51.544485Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:13:51.561495Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:14:00.844215Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:14:00.844233Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:14:51.844646Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2610")}
2025-07-27T14:14:51.844655Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:14:51.849815Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:15:52.144625Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2611")}
2025-07-27T14:15:52.144635Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:15:52.144654Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:16:52.345834Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2612")}
2025-07-27T14:16:52.345852Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:16:52.386575Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:16:52.444745Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2612")}
2025-07-27T14:16:52.444754Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:16:52.444805Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:17:52.744857Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2613")}
2025-07-27T14:17:52.744867Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:17:52.744898Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:18:53.044466Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2614")}
2025-07-27T14:18:53.044476Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:18:53.044492Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:19:05.943824Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:19:05.943839Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:19:38.444123Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:19:38.444146Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:19:53.244273Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2615")}
2025-07-27T14:19:53.244283Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:19:53.290808Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:19:53.344901Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2615")}
2025-07-27T14:19:53.344911Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:19:53.344930Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:20:11.144606Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:20:11.144615Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:20:35.444320Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:20:35.444333Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:20:53.543904Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2616")}
2025-07-27T14:20:53.543916Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:20:53.545592Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:21:33.343228Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:21:33.343247Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:21:45.744626Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:21:45.744660Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:21:53.743605Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2617")}
2025-07-27T14:21:53.743615Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:21:53.753690Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:22:19.843851Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:22:19.843879Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:22:54.043949Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2618")}
2025-07-27T14:22:54.043958Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:22:54.043975Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:23:31.542882Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/page.tsx")}
2025-07-27T14:23:31.542892Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:23:42.942560Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/page.tsx")}
2025-07-27T14:23:42.942569Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:23:54.242554Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2619")}
2025-07-27T14:23:54.242566Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:23:54.242586Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:24:54.543373Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2620")}
2025-07-27T14:24:54.543412Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:24:54.543443Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:25:52.165468Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/page.tsx")}
2025-07-27T14:25:52.165478Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:25:54.768521Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2621")}
2025-07-27T14:25:54.768532Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:25:54.789681Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:26:08.080211Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/page.tsx")}
2025-07-27T14:26:08.080231Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:26:55.090246Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2622")}
2025-07-27T14:26:55.090258Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:26:55.090284Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:27:55.290127Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2623")}
2025-07-27T14:27:55.290136Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:27:55.308157Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:27:55.390419Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/page.tsx")}
2025-07-27T14:27:55.390442Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:28:55.590839Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2624")}
2025-07-27T14:28:55.590848Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:28:55.590866Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:29:33.291351Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/page.tsx")}
2025-07-27T14:29:33.291371Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:29:55.790970Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2625")}
2025-07-27T14:29:55.790981Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:29:55.833899Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:29:55.891092Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2625")}
2025-07-27T14:29:55.891104Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:29:55.891124Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:30:56.091197Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2626")}
2025-07-27T14:30:56.091208Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:30:56.091228Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:31:56.291919Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2627")}
2025-07-27T14:31:56.291929Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:31:56.327260Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:32:05.592398Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/payload.config.ts")}
2025-07-27T14:32:05.592417Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:32:23.516634Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/collections/Users.ts")}
2025-07-27T14:32:23.516655Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:32:44.392819Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/auth/auth0-strategy.ts")}
2025-07-27T14:32:44.392854Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:32:56.492789Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2628")}
2025-07-27T14:32:56.492798Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:32:56.515586Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:33:15.693203Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/access/factories.ts")}
2025-07-27T14:33:15.693218Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:33:43.991881Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/collections/Products.ts")}
2025-07-27T14:33:43.991890Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:33:56.692171Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2629")}
2025-07-27T14:33:56.692181Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:33:56.732557Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:33:56.792725Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2629")}
2025-07-27T14:33:56.792734Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:33:56.792753Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:34:56.993310Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2630")}
2025-07-27T14:34:56.993319Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:34:56.993345Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:35:05.093771Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/payload.config.ts")}
2025-07-27T14:35:05.093781Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:35:19.193793Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/access/factories.ts")}
2025-07-27T14:35:19.193801Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:35:32.094336Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/collections/Products.ts")}
2025-07-27T14:35:32.094349Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:35:45.293837Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/auth/auth0-strategy.ts")}
2025-07-27T14:35:45.293845Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:35:57.193470Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2631")}
2025-07-27T14:35:57.193481Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:35:57.213750Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:36:21.794480Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/access/factories.ts")}
2025-07-27T14:36:21.794490Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:36:33.093885Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/collections/Products.ts")}
2025-07-27T14:36:33.093897Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:36:47.194743Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/payload.config.ts")}
2025-07-27T14:36:47.194751Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:36:57.493724Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2632")}
2025-07-27T14:36:57.493737Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:36:57.493765Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:37:08.894506Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/payload.config.ts")}
2025-07-27T14:37:08.894525Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:37:53.694622Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/payload.config.ts")}
2025-07-27T14:37:53.694638Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:37:57.694008Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2633")}
2025-07-27T14:37:57.694017Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:37:57.733252Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:37:57.794436Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2633")}
2025-07-27T14:37:57.794451Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:37:57.794476Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:38:24.895346Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/payload.config.ts")}
2025-07-27T14:38:24.895354Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:38:57.995199Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2634")}
2025-07-27T14:38:57.995211Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:38:57.995228Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:39:10.395276Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/payload.config.ts")}
2025-07-27T14:39:10.395286Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:39:58.194927Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2635")}
2025-07-27T14:39:58.194938Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:39:58.215619Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:40:44.295497Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/payload.config.ts")}
2025-07-27T14:40:44.295509Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:40:58.495764Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2636")}
2025-07-27T14:40:58.495792Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:40:58.495826Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:41:58.696311Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2637")}
2025-07-27T14:41:58.696320Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:41:58.696335Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:42:13.898047Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/edit/page.tsx")}
2025-07-27T14:42:13.898057Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:42:58.893508Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2638")}
2025-07-27T14:42:58.893528Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:42:58.907329Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:43:59.092819Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2639")}
2025-07-27T14:43:59.092882Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:43:59.125733Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:44:59.293111Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2640")}
2025-07-27T14:44:59.293159Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:44:59.331188Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:45:59.493493Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2641")}
2025-07-27T14:45:59.493523Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:45:59.525276Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:46:59.694294Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2642")}
2025-07-27T14:46:59.694305Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:46:59.701531Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:47:59.894446Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2643")}
2025-07-27T14:47:59.894467Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:47:59.904286Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:49:00.095032Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2644")}
2025-07-27T14:49:00.095041Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:49:00.095064Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:50:00.294991Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2645")}
2025-07-27T14:50:00.295001Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:50:00.295512Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:51:00.495863Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2646")}
2025-07-27T14:51:00.495873Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:51:00.505713Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:52:00.695163Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2647")}
2025-07-27T14:52:00.695172Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:52:00.713110Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:53:00.896121Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2648")}
2025-07-27T14:53:00.896130Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:53:00.933722Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:53:00.996866Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2648")}
2025-07-27T14:53:00.996873Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:53:00.996927Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:54:01.197490Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2649")}
2025-07-27T14:54:01.197500Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:54:01.197518Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:55:01.397041Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2650")}
2025-07-27T14:55:01.397078Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:55:01.420343Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:56:01.697550Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2651")}
2025-07-27T14:56:01.697558Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:56:01.697575Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:57:01.898737Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2652")}
2025-07-27T14:57:01.898764Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:57:01.932331Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:58:02.097610Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2653")}
2025-07-27T14:58:02.097645Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:58:02.128585Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:58:02.197732Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2653")}
2025-07-27T14:58:02.197742Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:58:02.197754Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T14:58:29.899762Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-07-27T14:58:29.899816Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-07-27T14:58:31.203714Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-07-27T14:58:31.203731Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-07-27T14:58:33.904180Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/package.json")}
2025-07-27T14:58:33.904191Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T14:58:34.100094Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("package-lock.json")}
2025-07-27T14:58:34.100931Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T14:59:02.399128Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2654")}
2025-07-27T14:59:02.399140Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T14:59:02.399160Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:00:02.598241Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2655")}
2025-07-27T15:00:02.598256Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:00:02.598275Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:01:02.799292Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2656")}
2025-07-27T15:01:02.799300Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:01:02.845541Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:01:02.899995Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2656")}
2025-07-27T15:01:02.900005Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:01:02.900041Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:01:37.399031Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/access/factories.ts")}
2025-07-27T15:01:37.399039Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:01:51.299490Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/access/users.ts")}
2025-07-27T15:01:51.299501Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:02:03.099484Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2657")}
2025-07-27T15:02:03.099505Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:02:03.099527Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:02:08.100236Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/collections/Categories.ts")}
2025-07-27T15:02:08.100270Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:02:24.299578Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/collections/Factories.ts")}
2025-07-27T15:02:24.299587Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:02:46.000015Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/collections/Media.ts")}
2025-07-27T15:02:46.000085Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:03:03.300734Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2658")}
2025-07-27T15:03:03.300753Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:03:03.340041Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:03:03.400136Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2658")}
2025-07-27T15:03:03.400159Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:03:03.400183Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:03:04.900577Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/collections/Products.ts")}
2025-07-27T15:03:04.900585Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:03:25.300027Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/collections/Users.ts")}
2025-07-27T15:03:25.300037Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:03:42.999504Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/globals/FactorySettings.ts")}
2025-07-27T15:03:42.999516Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:04:03.601134Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2659")}
2025-07-27T15:04:03.601143Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:04:03.601165Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:04:06.200342Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/hooks/auth0-sync.ts")}
2025-07-27T15:04:06.200354Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:05:03.801272Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2660")}
2025-07-27T15:05:03.801289Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:05:03.813260Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:05:09.801436Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/payload/collections/Media.ts")}
2025-07-27T15:05:09.801444Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:06:04.100318Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2661")}
2025-07-27T15:06:04.100373Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:06:04.100407Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:07:04.301355Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2662")}
2025-07-27T15:07:04.301381Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:07:04.301417Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:08:04.501415Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2663")}
2025-07-27T15:08:04.501424Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:08:04.508642Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:09:04.701593Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2664")}
2025-07-27T15:09:04.701605Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:09:04.721755Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:10:04.899244Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2665")}
2025-07-27T15:10:04.899266Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:10:04.933726Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:11:05.182815Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2666")}
2025-07-27T15:11:05.182825Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:11:05.182841Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:12:05.381851Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2667")}
2025-07-27T15:12:05.381861Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:12:05.390385Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:12:42.782252Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/completed/TYPESCRIPT-ERROR-CLEANUP-MILESTONE-COMPLETE.md")}
2025-07-27T15:12:42.782261Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:13:05.583732Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2668")}
2025-07-27T15:13:05.583808Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:13:05.612491Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:13:33.783055Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/completed/PAYLOAD-CMS-VERSION-MANAGEMENT-RESOLUTION-COMPLETE.md")}
2025-07-27T15:13:33.783065Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:13:54.282792Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/active-guides/PROJECT-ROADMAP-STATUS.md")}
2025-07-27T15:13:54.282801Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:14:05.882779Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2669")}
2025-07-27T15:14:05.882788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:14:05.882804Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:14:15.383308Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/active-guides/PROJECT-ROADMAP-STATUS.md")}
2025-07-27T15:14:15.383318Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:14:29.883194Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/active-guides/PROJECT-ROADMAP-STATUS.md")}
2025-07-27T15:14:29.883203Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:14:47.183391Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/active-guides/PROJECT-ROADMAP-STATUS.md")}
2025-07-27T15:14:47.183399Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:14:59.582645Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/active-guides/PROJECT-ROADMAP-STATUS.md")}
2025-07-27T15:14:59.582654Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:15:06.082507Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2670")}
2025-07-27T15:15:06.082518Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:15:06.108691Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:15:21.582223Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/active-guides/PROJECT-ROADMAP-STATUS.md")}
2025-07-27T15:15:21.582233Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:15:37.983104Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/active-guides/PROJECT-ROADMAP-STATUS.md")}
2025-07-27T15:15:37.983113Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:15:56.782812Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/active-guides/PROJECT-ROADMAP-STATUS.md")}
2025-07-27T15:15:56.782834Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:16:06.382674Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2671")}
2025-07-27T15:16:06.382700Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:16:06.382819Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:16:12.482661Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/active-guides/PROJECT-ROADMAP-STATUS.md")}
2025-07-27T15:16:12.482675Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:17:02.482858Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("README.md")}
2025-07-27T15:17:02.483123Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:17:06.583036Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2672")}
2025-07-27T15:17:06.583045Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:17:06.587138Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:17:36.887954Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/README.md")}
2025-07-27T15:17:36.887965Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:17:51.483903Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/README.md")}
2025-07-27T15:17:51.483912Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:18:06.883158Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2673")}
2025-07-27T15:18:06.883179Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:18:06.923024Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:18:06.983147Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2673")}
2025-07-27T15:18:06.983154Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:18:06.983170Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:18:08.284338Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/README.md")}
2025-07-27T15:18:08.284346Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:18:27.382310Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/README.md")}
2025-07-27T15:18:27.382319Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:19:07.484007Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2674")}
2025-07-27T15:19:07.484015Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:19:07.484031Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:19:30.084295Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/README.md")}
2025-07-27T15:19:30.084303Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:19:47.884741Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("docs/README.md")}
2025-07-27T15:19:47.884750Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:20:07.984523Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2675")}
2025-07-27T15:20:07.984531Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:20:07.984547Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:20:43.283480Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/README.md")}
2025-07-27T15:20:43.283504Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:21:08.383114Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2676")}
2025-07-27T15:21:08.383123Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:21:08.403509Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:21:38.484859Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/README.md")}
2025-07-27T15:21:38.484870Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:22:08.882752Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2677")}
2025-07-27T15:22:08.882760Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:22:08.882775Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:22:41.483583Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/mobile/README.md")}
2025-07-27T15:22:41.483592Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-07-27T15:23:09.383801Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2678")}
2025-07-27T15:23:09.383809Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:23:09.391210Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:24:09.584009Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2679")}
2025-07-27T15:24:09.584059Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:24:09.618044Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:24:09.684204Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2679")}
2025-07-27T15:24:09.684212Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:24:09.684233Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:25:09.783265Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2680")}
2025-07-27T15:25:09.783329Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:25:09.803937Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:26:10.084445Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2681")}
2025-07-27T15:26:10.084454Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:26:10.084565Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:27:10.283734Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2682")}
2025-07-27T15:27:10.283748Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:27:10.302132Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:28:10.588855Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2683")}
2025-07-27T15:28:10.588864Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:28:10.588878Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:29:10.790126Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2684")}
2025-07-27T15:29:10.790134Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:29:10.790165Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:30:10.989830Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2685")}
2025-07-27T15:30:10.989839Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:30:10.989875Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:31:11.190016Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2686")}
2025-07-27T15:31:11.190036Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:31:11.221293Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:31:36.791168Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/[id]/route.ts")}
2025-07-27T15:31:36.791184Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:31:51.890258Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/[id]/route.ts")}
2025-07-27T15:31:51.890268Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:32:04.891422Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/[id]/route.ts")}
2025-07-27T15:32:04.891431Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:32:11.491588Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2687")}
2025-07-27T15:32:11.491598Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:32:11.491638Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:32:17.291321Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/[id]/route.ts")}
2025-07-27T15:32:17.291330Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:32:28.891326Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/[id]/route.ts")}
2025-07-27T15:32:28.891335Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:32:41.291555Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/[id]/route.ts")}
2025-07-27T15:32:41.291565Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:32:53.001802Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/[id]/route.ts")}
2025-07-27T15:32:53.001814Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:33:05.290726Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/[id]/route.ts")}
2025-07-27T15:33:05.290738Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:33:11.690966Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2688")}
2025-07-27T15:33:11.690979Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:33:11.724509Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:33:11.791644Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2688")}
2025-07-27T15:33:11.791651Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:33:11.791666Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:33:16.290638Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/[id]/route.ts")}
2025-07-27T15:33:16.290648Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:33:29.490455Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/[id]/route.ts")}
2025-07-27T15:33:29.490463Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:33:45.390831Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/[id]/route.ts")}
2025-07-27T15:33:45.390840Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:33:56.693963Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/orders/[id]/route.ts")}
2025-07-27T15:33:56.693976Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:34:11.991983Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2689")}
2025-07-27T15:34:11.991994Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:34:11.992013Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:34:51.291297Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T15:34:51.291322Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:35:12.191805Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2690")}
2025-07-27T15:35:12.191821Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:35:12.212850Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:35:20.690468Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T15:35:20.690477Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:35:20.790628Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T15:35:20.790650Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:35:20.790771Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:35:33.491538Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T15:35:33.491548Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:35:46.091759Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T15:35:46.091768Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:35:59.391963Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T15:35:59.391972Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:36:12.491257Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2691")}
2025-07-27T15:36:12.491267Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:36:12.491298Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:37:10.092057Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T15:37:10.092066Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:37:12.691067Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2692")}
2025-07-27T15:37:12.691075Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:37:12.719931Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:37:22.691097Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T15:37:22.691115Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:37:35.591097Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T15:37:35.591130Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:37:46.592323Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/api/quotes/[id]/route.ts")}
2025-07-27T15:37:46.592332Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:38:12.991833Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2693")}
2025-07-27T15:38:12.991844Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:38:12.991861Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:39:13.191615Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2694")}
2025-07-27T15:39:13.191638Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:39:13.191656Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:40:13.391370Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2695")}
2025-07-27T15:40:13.391379Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:40:13.391398Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:41:13.591898Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2696")}
2025-07-27T15:41:13.591907Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:41:13.591926Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:42:13.792802Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2697")}
2025-07-27T15:42:13.792819Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:42:13.823022Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:43:14.093278Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2698")}
2025-07-27T15:43:14.093287Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:43:14.093305Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:43:22.393466Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T15:43:22.393486Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:43:49.495244Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-07-27T15:43:49.495254Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-07-27T15:44:14.293390Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2699")}
2025-07-27T15:44:14.293431Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:44:14.340825Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:44:14.393022Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2699")}
2025-07-27T15:44:14.393032Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:44:14.393053Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:44:21.093120Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T15:44:21.093142Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:44:35.093298Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T15:44:35.093307Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:45:14.592890Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2700")}
2025-07-27T15:45:14.592913Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:45:14.592950Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:45:20.193093Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T15:45:20.193102Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:46:14.893223Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2701")}
2025-07-27T15:46:14.893237Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:46:14.893270Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:46:52.293733Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/orders/page.tsx")}
2025-07-27T15:46:52.293742Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:47:07.094091Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-07-27T15:47:07.094100Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-07-27T15:47:15.093110Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2702")}
2025-07-27T15:47:15.093124Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:47:15.115160Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:47:44.493588Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/messages/page.tsx")}
2025-07-27T15:47:44.493600Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:48:15.394359Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2703")}
2025-07-27T15:48:15.394372Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:48:15.394457Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:48:43.294466Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/messages/page.tsx")}
2025-07-27T15:48:43.294476Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:49:01.994654Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/messages/page.tsx")}
2025-07-27T15:49:01.994664Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:49:15.593745Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2704")}
2025-07-27T15:49:15.593817Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:49:15.640009Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:50:01.194927Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/orders/create/page.tsx")}
2025-07-27T15:50:01.194946Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:50:15.894330Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2705")}
2025-07-27T15:50:15.894341Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:50:15.894378Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:50:23.496391Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-07-27T15:50:23.496402Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-07-27T15:51:05.294855Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/page.tsx")}
2025-07-27T15:51:05.294865Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:51:16.094199Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2706")}
2025-07-27T15:51:16.094219Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:51:16.103345Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:51:43.793394Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/analytics/page.tsx")}
2025-07-27T15:51:43.793433Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:52:06.094930Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/products/page.tsx")}
2025-07-27T15:52:06.094950Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:52:16.293964Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2707")}
2025-07-27T15:52:16.293985Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:52:16.346311Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:52:16.394489Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2707")}
2025-07-27T15:52:16.394497Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:52:16.394513Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:52:36.894741Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/profile/page.tsx")}
2025-07-27T15:52:36.894750Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:53:04.394710Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/quotes/page.tsx")}
2025-07-27T15:53:04.394730Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:53:16.594801Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2708")}
2025-07-27T15:53:16.594815Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:53:16.598535Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:53:35.894603Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/recurring-orders/page.tsx")}
2025-07-27T15:53:35.894621Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:53:56.694436Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/page.tsx")}
2025-07-27T15:53:56.694457Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:54:16.695209Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/products/create/page.tsx")}
2025-07-27T15:54:16.695222Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:54:16.795245Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2709")}
2025-07-27T15:54:16.795256Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:54:16.820739Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:54:44.294334Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/quotes/create/page.tsx")}
2025-07-27T15:54:44.294343Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:55:06.495536Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/create/page.tsx")}
2025-07-27T15:55:06.495558Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:55:16.995290Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2710")}
2025-07-27T15:55:16.995299Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:55:17.041796Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:55:17.094849Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2710")}
2025-07-27T15:55:17.094857Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:55:17.094882Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:55:28.195838Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/products/[id]/page.tsx")}
2025-07-27T15:55:28.195847Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:55:50.994814Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/quotes/[id]/page.tsx")}
2025-07-27T15:55:50.994825Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:56:14.296249Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/templates/[id]/page.tsx")}
2025-07-27T15:56:14.296266Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:56:17.494933Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2711")}
2025-07-27T15:56:17.494942Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:56:17.494961Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:56:42.696473Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/orders/[id]/page.tsx")}
2025-07-27T15:56:42.696483Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:57:17.927300Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2712")}
2025-07-27T15:57:17.927320Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:57:17.927337Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:58:04.838687Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/products/page.tsx")}
2025-07-27T15:58:04.838711Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:58:18.138326Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2713")}
2025-07-27T15:58:18.138336Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:58:18.183236Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:58:18.237440Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2713")}
2025-07-27T15:58:18.237496Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:58:18.237557Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:59:04.140057Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T15:59:04.140074Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:59:18.439650Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2714")}
2025-07-27T15:59:18.439658Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T15:59:18.439724Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T15:59:24.338883Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/layout.tsx")}
2025-07-27T15:59:24.338894Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T15:59:33.639313Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/page.tsx")}
2025-07-27T15:59:33.639322Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:00:18.639314Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2715")}
2025-07-27T16:00:18.639338Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:00:18.667418Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:01:18.940420Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2716")}
2025-07-27T16:01:18.940429Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:01:18.940444Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:01:45.840949Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/test/product-edit-integration/page.tsx")}
2025-07-27T16:01:45.840959Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:02:19.141716Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2717")}
2025-07-27T16:02:19.141766Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:02:19.164662Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:02:45.041816Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/login/page.tsx")}
2025-07-27T16:02:45.041826Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:03:02.643700Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-07-27T16:03:02.643709Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-07-27T16:03:19.442571Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2718")}
2025-07-27T16:03:19.442606Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:03:19.442649Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:03:42.242746Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/test/integration/page.tsx")}
2025-07-27T16:03:42.242809Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:04:19.643257Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2719")}
2025-07-27T16:04:19.643273Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:04:19.643293Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:04:39.043012Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/test/integration/page.tsx")}
2025-07-27T16:04:39.043030Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:05:19.843232Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2720")}
2025-07-27T16:05:19.843257Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:05:19.867751Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:06:01.743577Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/test/product-edit-integration/page.tsx"), AnchoredSystemPathBuf("apps/web/src/app/test/integration/page.tsx")}
2025-07-27T16:06:01.743587Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:06:01.843620Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/test/image-upload/page.tsx"), AnchoredSystemPathBuf("apps/web/src/app/test/page.tsx")}
2025-07-27T16:06:01.843658Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:06:01.843718Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:06:20.144297Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2721")}
2025-07-27T16:06:20.144311Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:06:20.144328Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:06:24.246290Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-07-27T16:06:24.246299Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-07-27T16:07:20.345794Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2722")}
2025-07-27T16:07:20.345809Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:07:20.345832Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:07:55.546218Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/onboarding/page.tsx")}
2025-07-27T16:07:55.546227Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:08:20.546597Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2723")}
2025-07-27T16:08:20.546616Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:08:20.569842Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:09:00.145431Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/page.tsx")}
2025-07-27T16:09:00.145447Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:09:20.846865Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2724")}
2025-07-27T16:09:20.846883Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:09:20.846907Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:09:57.947084Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/orders/create/page.tsx")}
2025-07-27T16:09:57.947098Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:10:21.047301Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2725")}
2025-07-27T16:10:21.047310Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:10:21.063954Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:11:11.047765Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/not-found.tsx")}
2025-07-27T16:11:11.047784Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:11:21.348850Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2726")}
2025-07-27T16:11:21.348888Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:11:21.348961Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:11:56.651272Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/login/page.tsx")}
2025-07-27T16:11:56.651285Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:12:21.549335Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2727")}
2025-07-27T16:12:21.549361Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:12:21.577694Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:13:21.746612Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2728")}
2025-07-27T16:13:21.746622Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:13:21.815652Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:13:21.847781Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2728")}
2025-07-27T16:13:21.847791Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:13:21.847829Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:13:48.547089Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/page.tsx")}
2025-07-27T16:13:48.547100Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:14:04.649010Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-07-27T16:14:04.649019Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-07-27T16:14:22.048045Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2729")}
2025-07-27T16:14:22.048055Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:14:22.048079Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:14:34.246992Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/page.tsx")}
2025-07-27T16:14:34.247036Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:14:47.547340Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/onboarding/page.tsx")}
2025-07-27T16:14:47.547349Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:15:22.247524Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2730")}
2025-07-27T16:15:22.247539Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:15:22.266197Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:15:54.448481Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T16:15:54.448492Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:16:22.448593Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2731")}
2025-07-27T16:16:22.448612Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:16:22.499303Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:16:22.549448Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2731")}
2025-07-27T16:16:22.549458Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:16:22.549477Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:16:34.749705Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/login/page.tsx")}
2025-07-27T16:16:34.749714Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:16:47.150508Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-07-27T16:16:47.150515Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-07-27T16:17:18.348830Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/login/page.tsx")}
2025-07-27T16:17:18.348842Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:17:22.749927Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2732")}
2025-07-27T16:17:22.749936Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:17:22.749952Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:17:29.450388Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T16:17:29.450400Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:18:22.949927Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2733")}
2025-07-27T16:18:22.949951Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:18:22.968238Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:19:23.151106Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2734")}
2025-07-27T16:19:23.151115Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:19:23.177269Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:20:23.452153Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2735")}
2025-07-27T16:20:23.452163Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:20:23.452182Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:21:21.852256Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T16:21:21.852267Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:21:23.652321Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2736")}
2025-07-27T16:21:23.652330Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:21:23.667230Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:21:58.052689Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T16:21:58.052697Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:22:23.953016Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2737")}
2025-07-27T16:22:23.953026Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:22:23.953054Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:22:32.353738Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/page.tsx")}
2025-07-27T16:22:32.353751Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:23:16.252877Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/template-library-client.tsx")}
2025-07-27T16:23:16.252887Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:23:24.153779Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2738")}
2025-07-27T16:23:24.153788Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:23:24.187570Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:23:24.253745Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2738")}
2025-07-27T16:23:24.253755Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:23:24.253777Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:23:42.653714Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/marketplace/template-library-client.tsx")}
2025-07-27T16:23:42.653722Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:24:24.455188Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2739")}
2025-07-27T16:24:24.455274Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:24:24.455306Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:24:50.455452Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/login/page.tsx")}
2025-07-27T16:24:50.455461Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:25:09.454662Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/login/login-client.tsx")}
2025-07-27T16:25:09.454671Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:25:24.654348Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2740")}
2025-07-27T16:25:24.654357Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:25:24.679055Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:26:01.856068Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/page.tsx")}
2025-07-27T16:26:01.856081Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:26:13.256510Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/home-client.tsx")}
2025-07-27T16:26:13.256519Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:26:24.856557Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2741")}
2025-07-27T16:26:24.856567Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:26:24.903222Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:26:24.956218Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2741")}
2025-07-27T16:26:24.956228Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:26:24.956260Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:27:05.257072Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/orders/create/page.tsx")}
2025-07-27T16:27:05.257089Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:27:25.156657Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2742")}
2025-07-27T16:27:25.156665Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:27:25.156711Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:27:34.657280Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/orders/create/page.tsx")}
2025-07-27T16:27:34.657288Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:28:10.957822Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/orders/create/page.tsx")}
2025-07-27T16:28:10.957831Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:28:25.357285Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2743")}
2025-07-27T16:28:25.357295Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:28:25.376918Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:28:50.557749Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/orders/create/create-order-client.tsx")}
2025-07-27T16:28:50.557758Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:29:18.857914Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/orders/create/create-order-client.tsx")}
2025-07-27T16:29:18.857924Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:29:25.658210Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2744")}
2025-07-27T16:29:25.658218Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:29:25.658236Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:29:33.859318Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("")}
2025-07-27T16:29:33.859327Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({}))
2025-07-27T16:30:13.758545Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/orders/page.tsx")}
2025-07-27T16:30:13.758610Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
2025-07-27T16:30:25.858459Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/.watchman-cookie-Dasso-2.local-41231-2745")}
2025-07-27T16:30:25.858468Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-07-27T16:30:25.864286Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-07-27T16:30:32.558729Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/web/src/app/dashboard/orders/page.tsx")}
2025-07-27T16:30:32.558738Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("web"), path: AnchoredSystemPathBuf("apps/web") }}))
